#!/usr/bin/env python3
"""
Setup script for WibeFit Backend
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check if pip is available
    if not shutil.which("pip"):
        print("❌ pip is not installed")
        return False
    print("✅ pip is available")
    
    return True

def setup_environment():
    """Set up the development environment"""
    print("\n🚀 Setting up WibeFit Backend...")
    
    if not check_requirements():
        print("❌ Requirements check failed")
        return False
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    # Create .env file if it doesn't exist
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
            print("⚠️  Please update .env file with your configuration")
        else:
            print("⚠️  .env.example not found, please create .env file manually")
    
    # Initialize Alembic if not already done
    if not os.path.exists("alembic/versions"):
        os.makedirs("alembic/versions", exist_ok=True)
        print("✅ Created Alembic versions directory")
    
    print("\n🎉 Backend setup completed!")
    print("\nNext steps:")
    print("1. Update .env file with your database and API credentials")
    print("2. Set up PostgreSQL database")
    print("3. Run: python -m alembic revision --autogenerate -m 'Initial migration'")
    print("4. Run: python -m alembic upgrade head")
    print("5. Start the server: python main.py")
    
    return True

def create_initial_migration():
    """Create initial database migration"""
    print("\n📊 Creating initial database migration...")
    
    if not run_command("python -m alembic revision --autogenerate -m 'Initial migration'", 
                      "Creating initial migration"):
        return False
    
    print("✅ Initial migration created")
    print("Run 'python -m alembic upgrade head' to apply migrations")
    return True

def main():
    """Main setup function"""
    print("🏋️ WibeFit Backend Setup")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "migrate":
        create_initial_migration()
    else:
        setup_environment()

if __name__ == "__main__":
    main()
