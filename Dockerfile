FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY . .

# Create database directory
RUN mkdir -p /app/data

# Copy database
COPY wibefit.db /app/data/wibefit.db

# Expose port
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app
ENV DATABASE_PATH=/app/data/wibefit.db

# Run the application
CMD ["python", "wibefit_sqlite_server.py"]