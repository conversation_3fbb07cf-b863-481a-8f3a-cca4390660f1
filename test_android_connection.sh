#!/bin/bash

echo "🤖 Testing Android Emulator Connection to WibeFit Backend"
echo "========================================================"

ANDROID_URL="http://192.168.1.14:8000"

echo ""
echo "1. Testing Health Check..."
curl -s -X GET "$ANDROID_URL/health" | python3 -m json.tool

echo ""
echo "2. Testing Registration with New User..."
TIMESTAMP=$(date +%s)
NEW_USERNAME="testuser$TIMESTAMP"
curl -s -X POST "$ANDROID_URL/api/auth/register-username" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$NEW_USERNAME\", \"password\": \"test123\"}" | python3 -m json.tool

echo ""
echo "3. Testing Login with Demo User..."
curl -s -X POST "$ANDROID_URL/api/auth/login-username" \
  -H "Content-Type: application/json" \
  -d '{"username": "demo", "password": "demo123"}' | python3 -m json.tool

echo ""
echo "4. Testing Profile Endpoint..."
TOKEN=$(curl -s -X POST "$ANDROID_URL/api/auth/login-username" \
  -H "Content-Type: application/json" \
  -d '{"username": "demo", "password": "demo123"}' | python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])")

curl -s -X GET "$ANDROID_URL/api/auth/profile" \
  -H "Authorization: Bearer $TOKEN" | python3 -m json.tool

echo ""
echo "✅ Android Emulator Connection Test Complete!"
echo "If all tests show JSON responses, the backend is working for Android."
