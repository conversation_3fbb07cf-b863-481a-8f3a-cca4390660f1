# 🚀 WibeFit Deployment Guide

This guide covers different deployment options for the WibeFit application.

## 📋 Prerequisites

- Git
- Docker & Docker Compose (for containerized deployment)
- PostgreSQL 12+ (for manual deployment)
- Python 3.8+ (for manual deployment)
- Flutter SDK (for mobile app development)

## 🐳 Docker Deployment (Recommended)

### Quick Start with Docker

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/wibefit_app.git
cd wibefit_app
```

2. **Start all services**
```bash
docker-compose up -d
```

3. **Check service status**
```bash
docker-compose ps
```

4. **View logs**
```bash
docker-compose logs -f backend
```

5. **Access the application**
- Backend API: http://localhost:8000
- Database: localhost:5432
- Health Check: http://localhost:8000/health

### Docker Configuration

The `docker-compose.yml` includes:
- **PostgreSQL**: Database with automatic schema initialization
- **Backend**: Python API server
- **Redis**: Caching and session storage

### Environment Variables

Copy and customize the environment file:
```bash
cp backend/.env.example backend/.env
# Edit backend/.env with your settings
```

## 🔧 Manual Deployment

### 1. Database Setup

**Install PostgreSQL:**
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS
brew install postgresql
brew services start postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

**Create database:**
```bash
createdb wibefit_db
psql -d wibefit_db -f database_schema.sql
```

### 2. Backend Setup

**Install dependencies:**
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

**Configure environment:**
```bash
cp .env.example .env
# Edit .env with your database credentials
```

**Start the server:**
```bash
python wibefit_postgresql_server.py
```

### 3. Frontend Setup

**Install Flutter dependencies:**
```bash
cd frontend
flutter pub get
```

**Configure API endpoint:**
Edit `frontend/lib/core/config/api_config.dart`:
```dart
class ApiConfig {
  static const String baseUrl = 'http://your-server:8000';
}
```

**Build for production:**
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release
```

## ☁️ Cloud Deployment

### Heroku Deployment

1. **Create Heroku app**
```bash
heroku create wibefit-app
heroku addons:create heroku-postgresql:hobby-dev
```

2. **Configure environment**
```bash
heroku config:set SECRET_KEY=your_secret_key
heroku config:set DEBUG=False
```

3. **Deploy**
```bash
git push heroku main
```

### AWS Deployment

1. **EC2 Instance Setup**
- Launch Ubuntu 20.04 LTS instance
- Install Docker and Docker Compose
- Clone repository and run docker-compose

2. **RDS Database**
- Create PostgreSQL RDS instance
- Update DATABASE_URL in environment

3. **Load Balancer**
- Configure Application Load Balancer
- Set up SSL certificate

### Google Cloud Platform

1. **Cloud Run Deployment**
```bash
gcloud run deploy wibefit-backend \
  --source=./backend \
  --platform=managed \
  --region=us-central1
```

2. **Cloud SQL**
```bash
gcloud sql instances create wibefit-db \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1
```

## 🔒 Security Considerations

### Production Settings

1. **Change default credentials**
```bash
# Generate secure secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

2. **Environment variables**
```env
DEBUG=False
SECRET_KEY=your_secure_secret_key
DATABASE_URL=postgresql://user:password@host:port/database
```

3. **Database security**
- Use strong passwords
- Enable SSL connections
- Restrict network access

### SSL/HTTPS Setup

1. **Nginx reverse proxy**
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

2. **Let's Encrypt certificate**
```bash
sudo certbot --nginx -d your-domain.com
```

## 📱 Mobile App Distribution

### Android

1. **Build signed APK**
```bash
flutter build apk --release
```

2. **Google Play Store**
- Create developer account
- Upload APK to Play Console
- Complete store listing

### iOS

1. **Build for App Store**
```bash
flutter build ios --release
```

2. **App Store Connect**
- Archive in Xcode
- Upload to App Store Connect
- Submit for review

## 🔍 Monitoring & Maintenance

### Health Checks

- Backend: `GET /health`
- Database: `SELECT 1`
- Redis: `PING`

### Logging

```bash
# Docker logs
docker-compose logs -f backend

# Manual deployment logs
tail -f backend/wibefit.log
```

### Backup

```bash
# Database backup
pg_dump wibefit_db > backup_$(date +%Y%m%d).sql

# Restore
psql wibefit_db < backup_20231201.sql
```

## 🆘 Troubleshooting

### Common Issues

1. **Database connection failed**
   - Check DATABASE_URL format
   - Verify PostgreSQL is running
   - Check firewall settings

2. **Flutter build errors**
   - Run `flutter clean && flutter pub get`
   - Check Flutter version compatibility

3. **API not accessible**
   - Verify server is running on correct port
   - Check CORS settings
   - Verify network connectivity

### Support

For deployment issues:
1. Check the logs first
2. Review environment configuration
3. Create an issue on GitHub
4. Contact <EMAIL>
