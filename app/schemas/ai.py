"""
AI service Pydantic schemas
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class PersonalizedWorkoutPlanRequest(BaseModel):
    user_preferences: Dict[str, Any] = Field(..., description="User preferences for workout plan")
    fitness_level: str = Field(..., description="User's fitness level (beginner, intermediate, advanced)")
    goals: List[str] = Field(..., description="User's fitness goals")
    available_equipment: Optional[List[str]] = Field(None, description="Available equipment")
    time_constraints: Optional[Dict[str, Any]] = Field(None, description="Time constraints")
    physical_limitations: Optional[List[str]] = Field(None, description="Physical limitations or injuries")


class PersonalizedWorkoutPlanResponse(BaseModel):
    workout_plan: Dict[str, Any] = Field(..., description="Generated workout plan")
    recommendations: List[str] = Field(..., description="Additional recommendations")
    estimated_duration: int = Field(..., description="Estimated duration in minutes")
    difficulty_level: str = Field(..., description="Difficulty level of the plan")


class WorkoutDataAnalysisRequest(BaseModel):
    workout_data: List[Dict[str, Any]] = Field(..., description="Workout data to analyze")
    analysis_type: str = Field(..., description="Type of analysis (performance, progress, trends)")
    time_period: Optional[str] = Field(None, description="Time period for analysis")


class WorkoutDataAnalysisResponse(BaseModel):
    analysis_results: Dict[str, Any] = Field(..., description="Analysis results")
    insights: List[str] = Field(..., description="Key insights from the analysis")
    recommendations: List[str] = Field(..., description="Recommendations based on analysis")
    performance_metrics: Dict[str, float] = Field(..., description="Performance metrics")


class ExerciseDetailsRequest(BaseModel):
    exercise_name: str = Field(..., description="Name of the exercise")
    user_level: Optional[str] = Field(None, description="User's fitness level")
    equipment_available: Optional[List[str]] = Field(None, description="Available equipment")


class ExerciseDetailsResponse(BaseModel):
    exercise_name: str = Field(..., description="Exercise name")
    description: str = Field(..., description="Exercise description")
    muscle_groups: List[str] = Field(..., description="Target muscle groups")
    equipment_needed: List[str] = Field(..., description="Required equipment")
    difficulty_level: str = Field(..., description="Difficulty level")
    calories_burned_per_minute: Optional[float] = Field(None, description="Estimated calories burned per minute")
    variations: Optional[List[Dict[str, str]]] = Field(None, description="Exercise variations")


class ExerciseInstructionsRequest(BaseModel):
    exercise_name: str = Field(..., description="Name of the exercise")
    user_level: Optional[str] = Field(None, description="User's fitness level")
    focus_areas: Optional[List[str]] = Field(None, description="Areas to focus on")


class ExerciseInstructionsResponse(BaseModel):
    exercise_name: str = Field(..., description="Exercise name")
    step_by_step_instructions: List[str] = Field(..., description="Step-by-step instructions")
    form_tips: List[str] = Field(..., description="Form and technique tips")
    common_mistakes: List[str] = Field(..., description="Common mistakes to avoid")
    safety_precautions: List[str] = Field(..., description="Safety precautions")
    modifications: Optional[List[Dict[str, str]]] = Field(None, description="Exercise modifications")


class MotivationalMessageRequest(BaseModel):
    user_context: Dict[str, Any] = Field(..., description="User context (progress, goals, mood)")
    message_type: str = Field(..., description="Type of message (encouragement, achievement, challenge)")
    personalization_data: Optional[Dict[str, Any]] = Field(None, description="Additional personalization data")


class MotivationalMessageResponse(BaseModel):
    message: str = Field(..., description="Motivational message")
    message_type: str = Field(..., description="Type of message")
    suggested_actions: Optional[List[str]] = Field(None, description="Suggested actions")
    motivational_quote: Optional[str] = Field(None, description="Related motivational quote")


class WorkoutLogsSummaryRequest(BaseModel):
    workout_logs: List[Dict[str, Any]] = Field(..., description="Workout logs to summarize")
    summary_period: str = Field(..., description="Summary period (daily, weekly, monthly)")
    focus_metrics: Optional[List[str]] = Field(None, description="Specific metrics to focus on")


class WorkoutLogsSummaryResponse(BaseModel):
    summary_period: str = Field(..., description="Summary period")
    total_workouts: int = Field(..., description="Total number of workouts")
    total_duration: int = Field(..., description="Total workout duration in minutes")
    calories_burned: float = Field(..., description="Total calories burned")
    performance_trends: Dict[str, Any] = Field(..., description="Performance trends")
    achievements: List[str] = Field(..., description="Achievements during the period")
    areas_for_improvement: List[str] = Field(..., description="Areas for improvement")
    next_period_goals: List[str] = Field(..., description="Suggested goals for next period")
