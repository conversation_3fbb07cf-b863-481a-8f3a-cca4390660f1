"""
Gym-related Pydantic schemas
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class GymBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    opening_hours: Optional[Dict[str, Any]] = None
    amenities: Optional[List[str]] = None
    membership_fees: Optional[Dict[str, float]] = None


class GymCreate(GymBase):
    pass


class GymUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    opening_hours: Optional[Dict[str, Any]] = None
    amenities: Optional[List[str]] = None
    membership_fees: Optional[Dict[str, float]] = None


class GymResponse(GymBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Gym Admin Schemas
class GymAdminCreate(BaseModel):
    username: str


class GymAdminResponse(BaseModel):
    id: int
    gym_id: int
    username: str
    assigned_at: datetime

    class Config:
        from_attributes = True


# Gym Trainer Schemas
class GymTrainerCreate(BaseModel):
    username: str
    specializations: Optional[List[str]] = None
    certification: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[float] = None


class GymTrainerUpdate(BaseModel):
    specializations: Optional[List[str]] = None
    certification: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[float] = None


class GymTrainerResponse(BaseModel):
    id: int
    gym_id: int
    username: str
    specializations: Optional[List[str]] = None
    certification: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[float] = None
    assigned_at: datetime

    class Config:
        from_attributes = True


# Gym Member Schemas
class GymMemberCreate(BaseModel):
    username: str
    membership_type: str
    membership_start: Optional[datetime] = None
    membership_end: Optional[datetime] = None


class GymMemberUpdate(BaseModel):
    membership_type: Optional[str] = None
    membership_start: Optional[datetime] = None
    membership_end: Optional[datetime] = None
    is_active: Optional[bool] = None


class GymMemberResponse(BaseModel):
    id: int
    gym_id: int
    username: str
    membership_type: str
    membership_start: Optional[datetime] = None
    membership_end: Optional[datetime] = None
    is_active: bool
    joined_at: datetime

    class Config:
        from_attributes = True


# Gym Store Inventory Schemas
class GymStoreInventoryCreate(BaseModel):
    product_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: float = Field(..., gt=0)
    stock_quantity: int = Field(default=0, ge=0)
    sku: Optional[str] = None
    image_url: Optional[str] = None
    is_available: bool = True


class GymStoreInventoryUpdate(BaseModel):
    product_name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: Optional[float] = Field(None, gt=0)
    stock_quantity: Optional[int] = Field(None, ge=0)
    sku: Optional[str] = None
    image_url: Optional[str] = None
    is_available: Optional[bool] = None


class GymStoreInventoryResponse(BaseModel):
    id: int
    gym_id: int
    product_name: str
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: float
    stock_quantity: int
    sku: Optional[str] = None
    image_url: Optional[str] = None
    is_available: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
