"""
Complete Authentication Service with Database Integration
"""

from sqlalchemy.orm import Session
from fastapi import HTTP<PERSON>x<PERSON>, status
from datetime import timed<PERSON><PERSON>
from typing import Optional, Dict, Any
import logging

from ..core.security import verify_password, get_password_hash, create_access_token
from ..core.config import settings
from ..models.user import User as UserModel, UserProfile
from ..schemas.user import UserCreate, UsernameRegister, AuthResponse

logger = logging.getLogger(__name__)


class AuthService:
    """Complete authentication service with database integration"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def authenticate_user_by_username(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user by username and password
        Returns user data if successful, None if failed
        """
        try:
            logger.info(f"🔍 Authenticating user: {username}")
            
            # Find user by username
            db_user = self.db.query(UserModel).filter(UserModel.username == username).first()
            
            if not db_user:
                logger.warning(f"❌ User not found: {username}")
                return None
            
            if not db_user.hashed_password:
                logger.warning(f"❌ User has no password: {username}")
                return None
            
            if not db_user.is_active:
                logger.warning(f"❌ User is inactive: {username}")
                return None
            
            # Verify password
            if not verify_password(password, db_user.hashed_password):
                logger.warning(f"❌ Invalid password for user: {username}")
                return None
            
            logger.info(f"✅ Authentication successful: {username}")
            
            # Return user data (excluding password hash)
            return {
                "username": db_user.username,
                "email": db_user.email,
                "full_name": db_user.full_name,
                "role": db_user.role,
                "is_active": db_user.is_active,
                "is_verified": db_user.is_verified,
                "profile_picture": db_user.profile_picture,
                "bio": db_user.bio,
                "phone_number": db_user.phone_number,
                "timezone": db_user.timezone,
                "language": db_user.language,
                "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
                "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
            }
            
        except Exception as e:
            logger.error(f"❌ Authentication error for {username}: {e}")
            return None
    
    def authenticate_user_by_email(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user by email and password
        Returns user data if successful, None if failed
        """
        try:
            logger.info(f"🔍 Authenticating user by email: {email}")
            
            # Find user by email
            db_user = self.db.query(UserModel).filter(UserModel.email == email).first()
            
            if not db_user:
                logger.warning(f"❌ User not found: {email}")
                return None
            
            if not db_user.hashed_password:
                logger.warning(f"❌ User has no password: {email}")
                return None
            
            if not db_user.is_active:
                logger.warning(f"❌ User is inactive: {email}")
                return None
            
            # Verify password
            if not verify_password(password, db_user.hashed_password):
                logger.warning(f"❌ Invalid password for user: {email}")
                return None
            
            logger.info(f"✅ Authentication successful: {email}")
            
            # Return user data (excluding password hash)
            return {
                "username": db_user.username,
                "email": db_user.email,
                "full_name": db_user.full_name,
                "role": db_user.role,
                "is_active": db_user.is_active,
                "is_verified": db_user.is_verified,
                "profile_picture": db_user.profile_picture,
                "bio": db_user.bio,
                "phone_number": db_user.phone_number,
                "timezone": db_user.timezone,
                "language": db_user.language,
                "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
                "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
            }
            
        except Exception as e:
            logger.error(f"❌ Authentication error for {email}: {e}")
            return None
    
    def register_user_with_username(self, username: str, password: str) -> Dict[str, Any]:
        """
        Register a new user with username and password
        Returns user data and access token
        """
        try:
            logger.info(f"🔄 Registering user: {username}")
            
            # Check if username already exists
            existing_user = self.db.query(UserModel).filter(UserModel.username == username).first()
            if existing_user:
                logger.warning(f"❌ Username already exists: {username}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
            
            # Hash password
            hashed_password = get_password_hash(password)
            logger.info(f"🔐 Password hashed for user: {username}")
            
            # Create new user
            db_user = UserModel(
                username=username,
                email=None,  # Email is optional for username registration
                full_name=username,  # Use username as display name initially
                hashed_password=hashed_password,
                role="member",
                is_active=True,
                is_verified=False
            )
            
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            
            # Create empty user profile
            user_profile = UserProfile(username=db_user.username)
            self.db.add(user_profile)
            self.db.commit()
            self.db.refresh(user_profile)
            
            logger.info(f"✅ User registered successfully: {username}")
            
            # Generate access token
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = create_access_token(
                data={"sub": db_user.username, "role": db_user.role},
                expires_delta=access_token_expires
            )
            
            # Return user data and token
            user_data = {
                "username": db_user.username,
                "email": db_user.email,
                "full_name": db_user.full_name,
                "role": db_user.role,
                "is_active": db_user.is_active,
                "is_verified": db_user.is_verified,
                "created_at": db_user.created_at.isoformat() if db_user.created_at else None
            }
            
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "refresh_token": f"refresh_{username}_{access_token[-10:]}",
                "user": user_data
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Registration error for {username}: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Registration failed: {str(e)}"
            )
    
    def create_access_token_for_user(self, user_data: Dict[str, Any]) -> str:
        """Create access token for authenticated user"""
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_data["username"], "role": user_data["role"]},
            expires_delta=access_token_expires
        )
        return access_token
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user data by username"""
        try:
            db_user = self.db.query(UserModel).filter(UserModel.username == username).first()
            if not db_user:
                return None
            
            return {
                "username": db_user.username,
                "email": db_user.email,
                "full_name": db_user.full_name,
                "role": db_user.role,
                "is_active": db_user.is_active,
                "is_verified": db_user.is_verified,
                "profile_picture": db_user.profile_picture,
                "bio": db_user.bio,
                "phone_number": db_user.phone_number,
                "timezone": db_user.timezone,
                "language": db_user.language,
                "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
                "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
            }
        except Exception as e:
            logger.error(f"❌ Error getting user {username}: {e}")
            return None
    
    def check_username_availability(self, username: str) -> bool:
        """Check if username is available"""
        try:
            existing_user = self.db.query(UserModel).filter(UserModel.username == username).first()
            return existing_user is None
        except Exception as e:
            logger.error(f"❌ Error checking username availability: {e}")
            return False
