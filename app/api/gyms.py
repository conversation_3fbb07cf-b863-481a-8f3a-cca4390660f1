"""
Gym API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging
import json

from ..core.database import get_db
from ..models.gym import Gym, GymAdmin, GymTrainer, GymMember, GymStoreInventory
from ..schemas.gym import (
    GymCreate, GymUpdate, GymResponse,
    GymAdminCreate, GymAdminResponse,
    GymTrainerCreate, GymTrainerUpdate, GymTrainerResponse,
    GymMemberCreate, GymMemberUpdate, GymMemberResponse,
    GymStoreInventoryCreate, GymStoreInventoryUpdate, GymStoreInventoryResponse
)
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/gyms", tags=["gyms"])


# Gym CRUD Operations
@router.get("/", response_model=List[GymResponse])
async def get_gyms(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """List all gyms"""
    gyms = db.query(Gym).all()
    return gyms


@router.post("/", response_model=GymResponse)
async def create_gym(
    gym_data: GymCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new gym"""
    try:
        # Convert lists/dicts to JSON strings for storage
        gym_dict = gym_data.model_dump()
        if gym_dict.get('opening_hours'):
            gym_dict['opening_hours'] = json.dumps(gym_dict['opening_hours'])
        if gym_dict.get('amenities'):
            gym_dict['amenities'] = json.dumps(gym_dict['amenities'])
        if gym_dict.get('membership_fees'):
            gym_dict['membership_fees'] = json.dumps(gym_dict['membership_fees'])
        
        gym = Gym(**gym_dict)
        db.add(gym)
        db.commit()
        db.refresh(gym)
        
        logger.info(f"Gym {gym.id} created by user {current_user.get('username')}")
        return gym
        
    except Exception as e:
        logger.error(f"Error creating gym: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create gym"
        )


@router.get("/{gymId}", response_model=GymResponse)
async def get_gym(
    gymId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get gym by ID"""
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )
    return gym


@router.put("/{gymId}", response_model=GymResponse)
async def update_gym(
    gymId: int,
    gym_update: GymUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update a gym"""
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )
    
    try:
        # Update gym fields
        update_data = gym_update.model_dump(exclude_unset=True)
        
        # Convert lists/dicts to JSON strings for storage
        if 'opening_hours' in update_data and update_data['opening_hours']:
            update_data['opening_hours'] = json.dumps(update_data['opening_hours'])
        if 'amenities' in update_data and update_data['amenities']:
            update_data['amenities'] = json.dumps(update_data['amenities'])
        if 'membership_fees' in update_data and update_data['membership_fees']:
            update_data['membership_fees'] = json.dumps(update_data['membership_fees'])
        
        for field, value in update_data.items():
            setattr(gym, field, value)
        
        db.commit()
        db.refresh(gym)
        
        logger.info(f"Gym {gymId} updated by user {current_user.get('username')}")
        return gym
        
    except Exception as e:
        logger.error(f"Error updating gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update gym"
        )


@router.delete("/{gymId}")
async def delete_gym(
    gymId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Delete a gym"""
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )
    
    try:
        db.delete(gym)
        db.commit()
        
        logger.info(f"Gym {gymId} deleted by user {current_user.get('username')}")
        return {"message": "Gym deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete gym"
        )


# Gym Admin Management
@router.post("/{gymId}/admins", response_model=GymAdminResponse)
async def add_gym_admin(
    gymId: int,
    admin_data: GymAdminCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add an admin to a gym"""
    # Check if gym exists
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )
    
    # Check if user is already an admin
    existing_admin = db.query(GymAdmin).filter(
        GymAdmin.gym_id == gymId,
        GymAdmin.username == admin_data.username
    ).first()

    if existing_admin:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already an admin of this gym"
        )

    try:
        gym_admin = GymAdmin(gym_id=gymId, username=admin_data.username)
        db.add(gym_admin)
        db.commit()
        db.refresh(gym_admin)

        logger.info(f"User {admin_data.username} added as admin to gym {gymId}")
        return gym_admin
        
    except Exception as e:
        logger.error(f"Error adding admin to gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add admin to gym"
        )


@router.delete("/{gymId}/admins/{username}")
async def remove_gym_admin(
    gymId: int,
    username: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove an admin from a gym"""
    gym_admin = db.query(GymAdmin).filter(
        GymAdmin.gym_id == gymId,
        GymAdmin.username == username
    ).first()

    if not gym_admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin association not found"
        )

    try:
        db.delete(gym_admin)
        db.commit()

        logger.info(f"User {username} removed as admin from gym {gymId}")
        return {"message": "Admin removed successfully"}

    except Exception as e:
        logger.error(f"Error removing admin from gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove admin from gym"
        )


# Gym Trainer Management
@router.post("/{gymId}/trainers", response_model=GymTrainerResponse)
async def add_gym_trainer(
    gymId: int,
    trainer_data: GymTrainerCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add a trainer to a gym"""
    # Check if gym exists
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )

    # Check if user is already a trainer
    existing_trainer = db.query(GymTrainer).filter(
        GymTrainer.gym_id == gymId,
        GymTrainer.username == trainer_data.username
    ).first()

    if existing_trainer:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already a trainer at this gym"
        )

    try:
        trainer_dict = trainer_data.model_dump()
        if trainer_dict.get('specializations'):
            trainer_dict['specializations'] = json.dumps(trainer_dict['specializations'])

        gym_trainer = GymTrainer(gym_id=gymId, **trainer_dict)
        db.add(gym_trainer)
        db.commit()
        db.refresh(gym_trainer)

        logger.info(f"User {trainer_data.username} added as trainer to gym {gymId}")
        return gym_trainer

    except Exception as e:
        logger.error(f"Error adding trainer to gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add trainer to gym"
        )


@router.delete("/{gymId}/trainers/{username}")
async def remove_gym_trainer(
    gymId: int,
    username: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove a trainer from a gym"""
    gym_trainer = db.query(GymTrainer).filter(
        GymTrainer.gym_id == gymId,
        GymTrainer.username == username
    ).first()

    if not gym_trainer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trainer association not found"
        )

    try:
        db.delete(gym_trainer)
        db.commit()

        logger.info(f"User {username} removed as trainer from gym {gymId}")
        return {"message": "Trainer removed successfully"}

    except Exception as e:
        logger.error(f"Error removing trainer from gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove trainer from gym"
        )


# Gym Member Management
@router.post("/{gymId}/members", response_model=GymMemberResponse)
async def add_gym_member(
    gymId: int,
    member_data: GymMemberCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add a member to a gym"""
    # Check if gym exists
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )

    # Check if user is already a member
    existing_member = db.query(GymMember).filter(
        GymMember.gym_id == gymId,
        GymMember.username == member_data.username
    ).first()

    if existing_member:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already a member of this gym"
        )

    try:
        gym_member = GymMember(gym_id=gymId, **member_data.model_dump())
        db.add(gym_member)
        db.commit()
        db.refresh(gym_member)

        logger.info(f"User {member_data.username} added as member to gym {gymId}")
        return gym_member

    except Exception as e:
        logger.error(f"Error adding member to gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add member to gym"
        )


@router.delete("/{gymId}/members/{username}")
async def remove_gym_member(
    gymId: int,
    username: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove a member from a gym"""
    gym_member = db.query(GymMember).filter(
        GymMember.gym_id == gymId,
        GymMember.username == username
    ).first()

    if not gym_member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Member association not found"
        )

    try:
        db.delete(gym_member)
        db.commit()

        logger.info(f"User {username} removed as member from gym {gymId}")
        return {"message": "Member removed successfully"}

    except Exception as e:
        logger.error(f"Error removing member from gym {gymId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove member from gym"
        )


# Gym Store Inventory Management
@router.post("/{gymId}/store-inventory", response_model=GymStoreInventoryResponse)
async def add_store_product(
    gymId: int,
    product_data: GymStoreInventoryCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add a product to a gym's store"""
    # Check if gym exists
    gym = db.query(Gym).filter(Gym.id == gymId).first()
    if not gym:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gym not found"
        )

    try:
        product = GymStoreInventory(gym_id=gymId, **product_data.model_dump())
        db.add(product)
        db.commit()
        db.refresh(product)

        logger.info(f"Product {product.id} added to gym {gymId} store")
        return product

    except Exception as e:
        logger.error(f"Error adding product to gym {gymId} store: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add product to store"
        )


@router.put("/{gymId}/store-inventory/{productId}", response_model=GymStoreInventoryResponse)
async def update_store_product(
    gymId: int,
    productId: int,
    product_update: GymStoreInventoryUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update a gym's store inventory item"""
    product = db.query(GymStoreInventory).filter(
        GymStoreInventory.id == productId,
        GymStoreInventory.gym_id == gymId
    ).first()

    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )

    try:
        # Update product fields
        update_data = product_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(product, field, value)

        db.commit()
        db.refresh(product)

        logger.info(f"Product {productId} updated in gym {gymId} store")
        return product

    except Exception as e:
        logger.error(f"Error updating product {productId} in gym {gymId} store: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update product"
        )


@router.delete("/{gymId}/store-inventory/{productId}")
async def remove_store_product(
    gymId: int,
    productId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove a product from a gym's store"""
    product = db.query(GymStoreInventory).filter(
        GymStoreInventory.id == productId,
        GymStoreInventory.gym_id == gymId
    ).first()

    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )

    try:
        db.delete(product)
        db.commit()

        logger.info(f"Product {productId} removed from gym {gymId} store")
        return {"message": "Product removed successfully"}

    except Exception as e:
        logger.error(f"Error removing product {productId} from gym {gymId} store: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove product"
        )
