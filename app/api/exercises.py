"""
Exercise API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
import logging

from ..core.database import get_db
from ..models.exercise import Exercise
from ..schemas.exercise import ExerciseListResponse, ExerciseResponse
from .auth import get_current_user, get_current_user_optional

router = APIRouter()
logger = logging.getLogger(__name__)

# Exercise CRUD Operations

@router.get("/", response_model=ExerciseListResponse)
async def get_exercises(
    q: Optional[str] = Query(None, description="Search query"),
    muscle_groups: Optional[List[str]] = Query(None, description="Filter by muscle groups"),
    equipment: Optional[List[str]] = Query(None, description="Filter by equipment"),
    difficulty: Optional[List[str]] = Query(None, description="Filter by difficulty"),
    category: Optional[List[str]] = Query(None, description="Filter by category"),
    exercise_type: Optional[List[str]] = Query(None, description="Filter by exercise type"),
    is_favorite: Optional[bool] = Query(None, description="Filter user favorites"),
    limit: int = Query(20, ge=1, le=100, description="Number of exercises to return"),
    offset: int = Query(0, ge=0, description="Number of exercises to skip"),
    current_user: Optional[dict] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Get exercises with filtering and search"""
    
    try:
        # Start with base query
        query = db.query(Exercise).filter(Exercise.is_active == True)
        
        # Apply search filter
        if q:
            search_term = f"%{q.lower()}%"
            query = query.filter(
                Exercise.name.ilike(search_term) |
                Exercise.description.ilike(search_term)
            )
        
        # Apply muscle group filter
        if muscle_groups:
            for muscle_group in muscle_groups:
                query = query.filter(Exercise.muscle_groups.contains([muscle_group]))
        
        # Apply equipment filter
        if equipment:
            for equip in equipment:
                query = query.filter(Exercise.equipment.contains([equip]))
        
        # Apply difficulty filter
        if difficulty:
            query = query.filter(Exercise.difficulty.in_(difficulty))
        
        # Apply category filter
        if category:
            query = query.filter(Exercise.category.in_(category))
        
        # Apply exercise type filter
        if exercise_type:
            query = query.filter(Exercise.exercise_type.in_(exercise_type))
        
        # Apply favorites filter
        if is_favorite and current_user:
            try:
                favorites_subquery = text("""
                    SELECT exercise_id FROM user_exercise_favorites
                    WHERE user_id = :user_id
                """)
                favorited_ids = db.execute(favorites_subquery, {"user_id": current_user["id"]}).fetchall()
                favorited_exercise_ids = [row.exercise_id for row in favorited_ids]

                if favorited_exercise_ids:
                    query = query.filter(Exercise.id.in_(favorited_exercise_ids))
                else:
                    return {
                        "exercises": [],
                        "total": 0,
                        "limit": limit,
                        "offset": offset,
                        "has_more": False
                    }
            except Exception as e:
                # Handle case where user_exercise_favorites table doesn't exist
                logger.warning(f"Could not apply favorites filter (table may not exist): {e}")
                # Return empty result for favorites filter when table doesn't exist
                return {
                    "exercises": [],
                    "total": 0,
                    "limit": limit,
                    "offset": offset,
                    "has_more": False
                }
        
        # Get total count for pagination
        total = query.count()
        
        # Apply pagination
        exercises = query.offset(offset).limit(limit).all()
        
        # Get user's favorited exercises if user is authenticated
        favorited_exercise_ids = set()
        if current_user:
            try:
                favorites_query = text("""
                    SELECT exercise_id FROM user_exercise_favorites
                    WHERE user_id = :user_id
                """)
                favorited_results = db.execute(favorites_query, {"user_id": current_user["id"]}).fetchall()
                favorited_exercise_ids = {row.exercise_id for row in favorited_results}
            except Exception as e:
                # Handle case where user_exercise_favorites table doesn't exist
                logger.warning(f"Could not fetch user favorites (table may not exist): {e}")
                favorited_exercise_ids = set()
        
        # Convert to response format
        exercise_list = []
        for exercise in exercises:
            exercise_dict = {
                "id": exercise.id,
                "name": exercise.name,
                "slug": exercise.slug,
                "description": exercise.description,
                "instructions": exercise.instructions or [],
                "tips": exercise.tips or [],
                "muscle_groups": exercise.muscle_groups or [],
                "equipment": exercise.equipment or [],
                "difficulty": exercise.difficulty,
                "exercise_type": exercise.exercise_type,
                "category": exercise.category,
                "duration_minutes": exercise.duration_minutes,
                "calories_per_minute": exercise.calories_per_minute,
                "video_url": exercise.video_url,
                "image_url": exercise.image_url,
                "is_active": exercise.is_active,
                "created_by": exercise.created_by,
                "created_at": exercise.created_at,
                "updated_at": exercise.updated_at,
                "is_favorite": exercise.id in favorited_exercise_ids
            }
            exercise_list.append(exercise_dict)

        return {
            "exercises": exercise_list,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
        
    except Exception as e:
        logger.error(f"Error fetching exercises: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch exercises: {str(e)}")


@router.get("/favorites")
async def get_user_favorite_exercises(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's favorite exercises"""
    try:
        # Get favorite exercises with details
        favorites_query = text("""
            SELECT e.*, f.created_at as favorited_at
            FROM exercises e
            JOIN user_exercise_favorites f ON e.id = f.exercise_id
            WHERE f.user_id = :user_id AND e.is_active = true
            ORDER BY f.created_at DESC
            LIMIT :limit OFFSET :offset
        """)

        count_query = text("""
            SELECT COUNT(*) as total
            FROM exercises e
            JOIN user_exercise_favorites f ON e.id = f.exercise_id
            WHERE f.user_id = :user_id AND e.is_active = true
        """)

        favorites = db.execute(favorites_query, {
            "user_id": current_user["id"],
            "limit": limit,
            "offset": offset
        }).fetchall()

        total_result = db.execute(count_query, {"user_id": current_user["id"]}).fetchone()
        total = total_result.total if total_result else 0

        # Convert to response format
        exercise_list = []
        for row in favorites:
            exercise_dict = {
                "id": row.id,
                "name": row.name,
                "slug": row.slug,
                "description": row.description,
                "instructions": row.instructions or [],
                "tips": row.tips or [],
                "muscle_groups": row.muscle_groups or [],
                "equipment": row.equipment or [],
                "difficulty": row.difficulty,
                "exercise_type": row.exercise_type,
                "category": row.category,
                "duration_minutes": row.duration_minutes,
                "calories_per_minute": row.calories_per_minute,
                "video_url": row.video_url,
                "image_url": row.image_url,
                "is_active": row.is_active,
                "created_by": row.created_by,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "is_favorite": True,
                "favorited_at": row.favorited_at
            }
            exercise_list.append(exercise_dict)

        return {
            "exercises": exercise_list,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }

    except Exception as e:
        logger.error(f"Error fetching favorite exercises: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch favorite exercises")


@router.get("/recent")
async def get_recent_exercises(
    limit: int = Query(10, ge=1, le=50),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's recently accessed exercises"""
    try:
        # Get recent exercises (distinct, ordered by most recent access)
        recent_query = text("""
            SELECT DISTINCT ON (e.id) e.*, r.accessed_at
            FROM exercises e
            JOIN user_recent_exercises r ON e.id = r.exercise_id
            WHERE r.user_id = :user_id AND e.is_active = true
            ORDER BY e.id, r.accessed_at DESC
            LIMIT :limit
        """)

        recent_exercises = db.execute(recent_query, {
            "user_id": current_user["id"],
            "limit": limit
        }).fetchall()

        # Check which exercises are favorited
        if recent_exercises:
            exercise_ids = [row.id for row in recent_exercises]
            favorites_query = text("""
                SELECT exercise_id FROM user_exercise_favorites
                WHERE user_id = :user_id AND exercise_id = ANY(:exercise_ids)
            """)

            favorited_ids = db.execute(favorites_query, {
                "user_id": current_user["id"],
                "exercise_ids": exercise_ids
            }).fetchall()

            favorited_set = {row.exercise_id for row in favorited_ids}
        else:
            favorited_set = set()

        # Convert to response format
        exercise_list = []
        for row in recent_exercises:
            exercise_dict = {
                "id": row.id,
                "name": row.name,
                "slug": row.slug,
                "description": row.description,
                "instructions": row.instructions or [],
                "tips": row.tips or [],
                "muscle_groups": row.muscle_groups or [],
                "equipment": row.equipment or [],
                "difficulty": row.difficulty,
                "exercise_type": row.exercise_type,
                "category": row.category,
                "duration_minutes": row.duration_minutes,
                "calories_per_minute": row.calories_per_minute,
                "video_url": row.video_url,
                "image_url": row.image_url,
                "is_active": row.is_active,
                "created_by": row.created_by,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "is_favorite": row.id in favorited_set,
                "last_accessed": row.accessed_at
            }
            exercise_list.append(exercise_dict)

        return {
            "exercises": exercise_list,
            "total": len(exercise_list)
        }

    except Exception as e:
        logger.error(f"Error fetching recent exercises: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch recent exercises")


@router.get("/{exercise_id}")
async def get_exercise(
    exercise_id: int,
    current_user: Optional[dict] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Get a specific exercise by ID"""
    try:
        # Get exercise from database
        exercise = db.query(Exercise).filter(
            Exercise.id == exercise_id,
            Exercise.is_active == True
        ).first()
        
        if not exercise:
            raise HTTPException(status_code=404, detail="Exercise not found")
        
        # Check if user has favorited this exercise
        is_favorite = False
        if current_user:
            favorite_check = db.execute(text("""
                SELECT id FROM user_exercise_favorites 
                WHERE user_id = :user_id AND exercise_id = :exercise_id
            """), {"user_id": current_user["id"], "exercise_id": exercise_id}).fetchone()
            is_favorite = favorite_check is not None
        
        # Track exercise access for recent exercises
        if current_user:
            db.execute(text("""
                INSERT INTO user_recent_exercises (user_id, exercise_id, accessed_at) 
                VALUES (:user_id, :exercise_id, CURRENT_TIMESTAMP)
            """), {"user_id": current_user["id"], "exercise_id": exercise_id})
            db.commit()
        
        # Convert to response format
        exercise_dict = {
            "id": exercise.id,
            "name": exercise.name,
            "slug": exercise.slug,
            "description": exercise.description,
            "instructions": exercise.instructions or [],
            "tips": exercise.tips or [],
            "muscle_groups": exercise.muscle_groups or [],
            "equipment": exercise.equipment or [],
            "difficulty": exercise.difficulty,
            "exercise_type": exercise.exercise_type,
            "category": exercise.category,
            "duration_minutes": exercise.duration_minutes,
            "calories_per_minute": exercise.calories_per_minute,
            "video_url": exercise.video_url,
            "image_url": exercise.image_url,
            "is_active": exercise.is_active,
            "created_by": exercise.created_by,
            "created_at": exercise.created_at,
            "updated_at": exercise.updated_at,
            "is_favorite": is_favorite
        }
        
        return exercise_dict
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching exercise {exercise_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch exercise: {str(e)}")


# Favorites Management Endpoints

@router.post("/{exercise_id}/favorite")
async def add_exercise_to_favorites(
    exercise_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add exercise to user's favorites"""
    try:
        # Check if exercise exists
        exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
        if not exercise:
            raise HTTPException(status_code=404, detail="Exercise not found")
        
        # Check if already favorited
        existing = db.execute(text("""
            SELECT id FROM user_exercise_favorites 
            WHERE user_id = :user_id AND exercise_id = :exercise_id
        """), {"user_id": current_user["id"], "exercise_id": exercise_id}).fetchone()
        
        if existing:
            return {"message": "Exercise already in favorites", "is_favorite": True}
        
        # Add to favorites
        db.execute(text("""
            INSERT INTO user_exercise_favorites (user_id, exercise_id) 
            VALUES (:user_id, :exercise_id)
        """), {"user_id": current_user["id"], "exercise_id": exercise_id})
        
        db.commit()
        
        return {"message": "Exercise added to favorites", "is_favorite": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding exercise to favorites: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to add exercise to favorites")


@router.delete("/{exercise_id}/favorite")
async def remove_exercise_from_favorites(
    exercise_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove exercise from user's favorites"""
    try:
        # Remove from favorites
        result = db.execute(text("""
            DELETE FROM user_exercise_favorites 
            WHERE user_id = :user_id AND exercise_id = :exercise_id
        """), {"user_id": current_user["id"], "exercise_id": exercise_id})
        
        db.commit()
        
        if result.rowcount == 0:
            return {"message": "Exercise was not in favorites", "is_favorite": False}
        
        return {"message": "Exercise removed from favorites", "is_favorite": False}
        
    except Exception as e:
        logger.error(f"Error removing exercise from favorites: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to remove exercise from favorites")



