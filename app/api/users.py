"""
User API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from ..core.database import get_db
from ..core.gcs_storage import gcs_service
from ..models.user import User, UserProfile, UserMeasurement, UserGoal, UserAchievement
from ..schemas.user import (
    UserResponse, UserUpdate, UserProfileCreate, UserProfileUpdate, UserProfileResponse,
    UserMeasurementCreate, UserMeasurementResponse, UserGoalCreate, UserGoalUpdate,
    UserGoalResponse, UserStats, UserAnalytics
)
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/users", tags=["users"])


# User CRUD Operations
@router.get("/", response_model=List[UserResponse])
async def get_users(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """List all users"""
    users = db.query(User).all()
    return users


@router.get("/{userId}", response_model=UserResponse)
async def get_user_by_id(
    userId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get user by ID"""
    user = db.query(User).filter(User.id == userId).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user


@router.put("/{userId}", response_model=UserResponse)
async def update_user_by_id(
    userId: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update a user"""
    user = db.query(User).filter(User.id == userId).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    try:
        # Update user fields
        update_data = user_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)

        db.commit()
        db.refresh(user)

        logger.info(f"User {userId} updated by user {current_user.get('username')}")
        return user

    except Exception as e:
        logger.error(f"Error updating user {userId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: dict = Depends(get_current_user)
):
    """Get current user profile"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update current user profile"""
    try:
        # Update user fields
        for field, value in user_update.model_dump(exclude_unset=True).items():
            setattr(current_user, field, value)
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"User {current_user.id} profile updated")
        return current_user
        
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.post("/me/avatar")
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload user avatar"""
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an image"
            )
        
        # Upload to GCS
        avatar_url = gcs_service.upload_image(file.file, file.filename)
        if not avatar_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload avatar"
            )
        
        # Update user profile
        current_user.profile_picture = avatar_url
        db.commit()
        
        logger.info(f"Avatar uploaded for user {current_user.id}")
        return {"avatar_url": avatar_url}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading avatar: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload avatar"
        )


# User Profile endpoints
@router.get("/me/profile", response_model=UserProfileResponse)
async def get_user_profile(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's detailed profile"""
    profile = db.query(UserProfile).filter(UserProfile.username == current_user["username"]).first()
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    return profile


@router.post("/me/profile", response_model=UserProfileResponse)
async def create_user_profile(
    profile_data: UserProfileCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create user profile"""
    try:
        # Check if profile already exists
        existing_profile = db.query(UserProfile).filter(UserProfile.username == current_user["username"]).first()
        if existing_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User profile already exists"
            )

        # Create new profile
        profile = UserProfile(
            username=current_user["username"],
            **profile_data.model_dump(exclude_unset=True)
        )
        db.add(profile)
        db.commit()
        db.refresh(profile)

        logger.info(f"Profile created for user {current_user['username']}")
        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user profile: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user profile"
        )


@router.put("/me/profile", response_model=UserProfileResponse)
async def update_user_profile(
    profile_update: UserProfileUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    try:
        profile = db.query(UserProfile).filter(UserProfile.username == current_user["username"]).first()
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # Update profile fields
        for field, value in profile_update.model_dump(exclude_unset=True).items():
            setattr(profile, field, value)

        db.commit()
        db.refresh(profile)

        logger.info(f"Profile updated for user {current_user['username']}")
        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


# User Measurements endpoints
@router.get("/me/measurements", response_model=List[UserMeasurementResponse])
async def get_user_measurements(
    measurement_type: Optional[str] = None,
    limit: int = 50,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user measurements"""
    query = db.query(UserMeasurement).filter(UserMeasurement.username == current_user["username"])
    
    if measurement_type:
        query = query.filter(UserMeasurement.measurement_type == measurement_type)
    
    measurements = query.order_by(UserMeasurement.measured_at.desc()).limit(limit).all()
    return measurements


@router.post("/me/measurements", response_model=UserMeasurementResponse)
async def create_user_measurement(
    measurement_data: UserMeasurementCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create user measurement"""
    try:
        measurement = UserMeasurement(
            username=current_user["username"],
            **measurement_data.model_dump()
        )
        db.add(measurement)
        db.commit()
        db.refresh(measurement)
        
        logger.info(f"Measurement created for user {current_user['username']}")
        return measurement

    except Exception as e:
        logger.error(f"Error creating measurement: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create measurement"
        )


# User Goals endpoints
@router.get("/me/goals", response_model=List[UserGoalResponse])
async def get_user_goals(
    is_completed: Optional[bool] = None,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user goals"""
    query = db.query(UserGoal).filter(UserGoal.username == current_user["username"])
    
    if is_completed is not None:
        query = query.filter(UserGoal.is_completed == is_completed)
    
    goals = query.order_by(UserGoal.created_at.desc()).all()
    return goals


@router.post("/me/goals", response_model=UserGoalResponse)
async def create_user_goal(
    goal_data: UserGoalCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create user goal"""
    try:
        goal = UserGoal(
            username=current_user["username"],
            **goal_data.model_dump()
        )
        db.add(goal)
        db.commit()
        db.refresh(goal)

        logger.info(f"Goal created for user {current_user['username']}")
        return goal

    except Exception as e:
        logger.error(f"Error creating goal: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create goal"
        )


@router.put("/me/goals/{goal_id}", response_model=UserGoalResponse)
async def update_user_goal(
    goal_id: int,
    goal_update: UserGoalUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user goal"""
    try:
        goal = db.query(UserGoal).filter(
            UserGoal.id == goal_id,
            UserGoal.username == current_user["username"]
        ).first()

        if not goal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Goal not found"
            )

        # Update goal fields
        for field, value in goal_update.model_dump(exclude_unset=True).items():
            setattr(goal, field, value)

        db.commit()
        db.refresh(goal)

        logger.info(f"Goal {goal_id} updated for user {current_user['username']}")
        return goal
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating goal: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update goal"
        )


# User Statistics endpoints
@router.get("/me/stats", response_model=UserStats)
async def get_user_stats(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user statistics"""
    try:
        # Get achievements count from database
        achievements_count = db.query(UserAchievement).filter(
            UserAchievement.username == current_user["username"]
        ).count()

        # For now, return mock statistics with real achievements count
        # TODO: Implement actual workout tracking statistics
        return UserStats(
            total_workouts=24,
            total_calories_burned=2840.0,
            total_workout_time_minutes=1080.0,
            current_streak_days=7,
            longest_streak_days=14,
            favorite_exercise_category="strength",
            average_workout_duration=45.0,
            achievements_count=achievements_count,
            friends_count=8
        )
    except Exception as e:
        logger.error(f"Error fetching user stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch user statistics"
        )


@router.get("/me/analytics", response_model=UserAnalytics)
async def get_user_analytics(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user analytics"""
    # Mock analytics for now
    return UserAnalytics(
        weekly_workouts=[3, 4, 2, 5, 3, 4, 3],
        monthly_progress={
            "weight_loss": {"current": 2.5, "target": 5.0},
            "muscle_gain": {"current": 1.2, "target": 3.0}
        },
        goal_progress=[
            {"goal": "Weight Loss", "progress": 0.5, "target_date": "2024-06-01"},
            {"goal": "5K Run", "progress": 0.8, "target_date": "2024-05-15"}
        ],
        recent_achievements=[
            {"title": "First 5K", "earned_at": "2024-03-15", "points": 100},
            {"title": "Workout Streak", "earned_at": "2024-03-10", "points": 50}
        ]
    )


@router.get("/me/achievements")
async def get_user_achievements(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user achievements from database"""
    try:
        # Query user achievements from database
        achievements = db.query(UserAchievement).filter(
            UserAchievement.username == current_user["username"]
        ).all()

        # Convert to response format
        achievement_list = []
        for achievement in achievements:
            achievement_list.append({
                "id": achievement.id,
                "title": achievement.title,
                "description": achievement.description,
                "icon": achievement.icon,
                "points": achievement.points,
                "earned_at": achievement.earned_at.isoformat() if achievement.earned_at else None,
                "achievement_type": achievement.achievement_type
            })

        return {"achievements": achievement_list}
    except Exception as e:
        logger.error(f"Error fetching user achievements: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch achievements"
        )


@router.get("/me/exercises/favorites")
async def get_favorite_exercises_count(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get count of user's favorite exercises"""
    try:
        # For now, return mock data since we don't have exercise favorites implemented
        # TODO: Implement actual favorite exercises when exercise system is ready
        return {"count": 5}
    except Exception as e:
        logger.error(f"Error fetching favorite exercises count: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch favorite exercises count"
        )


@router.get("/me/exercises/recent")
async def get_recent_exercises_count(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get count of user's recent exercises"""
    try:
        # For now, return mock data since we don't have exercise tracking implemented
        # TODO: Implement actual recent exercises when exercise system is ready
        return {"count": 8}
    except Exception as e:
        logger.error(f"Error fetching recent exercises count: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch recent exercises count"
        )
