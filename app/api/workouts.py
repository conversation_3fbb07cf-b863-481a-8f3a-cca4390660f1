"""
Workout API routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from ..core.database import get_db
from ..models.workout import WorkoutPlan
from ..schemas.workout import WorkoutPlanCreate, WorkoutPlanUpdate, WorkoutPlanResponse
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/workouts", tags=["workouts"])

# Workout CRUD Operations
@router.get("/", response_model=List[WorkoutPlanResponse])
async def get_workouts(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """List all exercises (workouts)"""
    workouts = db.query(WorkoutPlan).all()
    return workouts


@router.post("/", response_model=WorkoutPlanResponse)
async def create_workout(
    workout_data: WorkoutPlanCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new exercise (workout)"""
    try:
        # Convert lists to JSON strings for storage if needed
        workout_dict = workout_data.model_dump()

        workout = WorkoutPlan(**workout_dict)
        db.add(workout)
        db.commit()
        db.refresh(workout)

        logger.info(f"Workout {workout.id} created by user {current_user.get('username')}")
        return workout

    except Exception as e:
        logger.error(f"Error creating workout: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workout"
        )


@router.get("/{workoutId}", response_model=WorkoutPlanResponse)
async def get_workout(
    workoutId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get exercise (workout) by ID"""
    workout = db.query(WorkoutPlan).filter(WorkoutPlan.id == workoutId).first()
    if not workout:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout not found"
        )
    return workout


@router.put("/{workoutId}", response_model=WorkoutPlanResponse)
async def update_workout(
    workoutId: int,
    workout_update: WorkoutPlanUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update an exercise (workout)"""
    workout = db.query(WorkoutPlan).filter(WorkoutPlan.id == workoutId).first()
    if not workout:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout not found"
        )

    try:
        # Update workout fields
        update_data = workout_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(workout, field, value)

        db.commit()
        db.refresh(workout)

        logger.info(f"Workout {workoutId} updated by user {current_user.get('username')}")
        return workout

    except Exception as e:
        logger.error(f"Error updating workout {workoutId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update workout"
        )


@router.delete("/{workoutId}")
async def delete_workout(
    workoutId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Delete an exercise (workout)"""
    workout = db.query(WorkoutPlan).filter(WorkoutPlan.id == workoutId).first()
    if not workout:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout not found"
        )

    try:
        db.delete(workout)
        db.commit()

        logger.info(f"Workout {workoutId} deleted by user {current_user.get('username')}")
        return {"message": "Workout deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting workout {workoutId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete workout"
        )



