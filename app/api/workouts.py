"""
Workout API routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from ..schemas.workout import WorkoutPlanResponse, WorkoutSession, WorkoutSessionCreate, WorkoutSessionUpdate
from ..schemas.exercise import ExerciseResponse
from .auth import get_current_user

router = APIRouter(prefix="/workouts", tags=["workouts"])

# Mock workout data
mock_exercises = [
    {
        "id": 1,
        "name": "Push-ups",
        "description": "Classic upper body exercise",
        "category": "strength",
        "muscle_groups": ["chest", "triceps", "shoulders"],
        "equipment": "bodyweight",
        "difficulty": "beginner",
        "instructions": "Start in plank position, lower body to ground, push back up",
        "video_url": "https://example.com/pushups.mp4",
        "image_url": "https://example.com/pushups.jpg",
        "created_at": "2024-01-01T00:00:00"
    },
    {
        "id": 2,
        "name": "Squats",
        "description": "Lower body strength exercise",
        "category": "strength",
        "muscle_groups": ["quadriceps", "glutes", "hamstrings"],
        "equipment": "bodyweight",
        "difficulty": "beginner",
        "instructions": "Stand with feet shoulder-width apart, lower hips back and down",
        "video_url": "https://example.com/squats.mp4",
        "image_url": "https://example.com/squats.jpg",
        "created_at": "2024-01-01T00:00:00"
    },
    {
        "id": 3,
        "name": "Jumping Jacks",
        "description": "Full body cardio exercise",
        "category": "cardio",
        "muscle_groups": ["full_body"],
        "equipment": "bodyweight",
        "difficulty": "beginner",
        "instructions": "Jump while spreading legs and raising arms overhead",
        "video_url": "https://example.com/jumping_jacks.mp4",
        "image_url": "https://example.com/jumping_jacks.jpg",
        "created_at": "2024-01-01T00:00:00"
    }
]

mock_workout_plans = [
    {
        "id": 1,
        "name": "Morning Cardio Blast",
        "slug": "morning-cardio-blast",
        "description": "High-intensity cardio workout to start your day",
        "creator_id": 2,
        "difficulty": "intermediate",
        "duration_minutes": 30,
        "category": "cardio",
        "is_public": True,
        "rating": 4.5,
        "total_ratings": 125,
        "total_completions": 1250,
        "exercises": [
            {"exercise_id": 3, "sets": 3, "reps": 30, "duration_seconds": 60},
            {"exercise_id": 1, "sets": 3, "reps": 15, "duration_seconds": None},
            {"exercise_id": 2, "sets": 3, "reps": 20, "duration_seconds": None}
        ],
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    },
    {
        "id": 2,
        "name": "Strength Foundation",
        "slug": "strength-foundation",
        "description": "Build strength with basic bodyweight exercises",
        "creator_id": 2,
        "difficulty": "beginner",
        "duration_minutes": 25,
        "category": "strength",
        "is_public": True,
        "rating": 4.2,
        "total_ratings": 89,
        "total_completions": 890,
        "exercises": [
            {"exercise_id": 1, "sets": 3, "reps": 10, "duration_seconds": None},
            {"exercise_id": 2, "sets": 3, "reps": 15, "duration_seconds": None}
        ],
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
]


# Import exercises router to delegate exercise endpoints
from . import exercises

# Delegate exercise endpoints to the exercises router
router.include_router(exercises.router, prefix="/exercises", tags=["exercises"])


@router.get("/plans", response_model=List[WorkoutPlanResponse])
async def get_workout_plans(
    difficulty: str = None,
    category: str = None,
    current_user: dict = Depends(get_current_user)
):
    """Get list of workout plans with optional filtering"""
    plans = mock_workout_plans.copy()
    
    if difficulty:
        plans = [p for p in plans if p["difficulty"] == difficulty]
    
    if category:
        plans = [p for p in plans if p["category"] == category]
    
    return plans


@router.get("/plans/{plan_id}", response_model=WorkoutPlanResponse)
async def get_workout_plan(
    plan_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get specific workout plan by ID"""
    plan = next((p for p in mock_workout_plans if p["id"] == plan_id), None)
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout plan not found"
        )
    return plan


@router.post("/sessions", response_model=WorkoutSession)
async def start_workout_session(
    session_data: WorkoutSessionCreate,
    current_user: dict = Depends(get_current_user)
):
    """Start a new workout session"""
    # Verify workout plan exists
    plan = next((p for p in mock_workout_plans if p["id"] == session_data.workout_plan_id), None)
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout plan not found"
        )
    
    # Create new session
    new_session = {
        "id": 1,  # In real app, this would be auto-generated
        "user_id": current_user["id"],
        "workout_plan_id": session_data.workout_plan_id,
        "started_at": "2024-01-01T00:00:00",
        "completed_at": None,
        "duration_minutes": None,
        "calories_burned": None,
        "notes": session_data.notes,
        "exercises_completed": None
    }
    
    return WorkoutSession(**new_session)


@router.get("/sessions/my", response_model=List[WorkoutSession])
async def get_my_workout_sessions(
    current_user: dict = Depends(get_current_user)
):
    """Get current user's workout sessions"""
    # Mock data - in real app, filter by user_id
    sessions = [
        {
            "id": 1,
            "user_id": current_user["id"],
            "workout_plan_id": 1,
            "started_at": "2024-01-01T08:00:00",
            "completed_at": "2024-01-01T08:30:00",
            "duration_minutes": 30,
            "calories_burned": 250,
            "notes": "Great workout!",
            "exercises_completed": [
                {"exercise_id": 3, "sets_completed": 3, "reps_completed": 30},
                {"exercise_id": 1, "sets_completed": 3, "reps_completed": 15},
                {"exercise_id": 2, "sets_completed": 3, "reps_completed": 20}
            ]
        }
    ]
    
    return sessions
