"""
Workout Plans API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from ..core.database import get_db
from ..models.workout import WorkoutPlan
from ..schemas.workout import WorkoutPlanCreate, WorkoutPlanUpdate, WorkoutPlanResponse
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/workout-plans", tags=["workout-plans"])


# Workout Plan CRUD Operations
@router.get("/", response_model=List[WorkoutPlanResponse])
async def get_workout_plans(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """List all workout plans"""
    workout_plans = db.query(WorkoutPlan).all()
    return workout_plans


@router.post("/", response_model=WorkoutPlanResponse)
async def create_workout_plan(
    plan_data: WorkoutPlanCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new workout plan"""
    try:
        # Convert lists to JSON strings for storage if needed
        plan_dict = plan_data.model_dump()
        
        workout_plan = WorkoutPlan(**plan_dict)
        db.add(workout_plan)
        db.commit()
        db.refresh(workout_plan)
        
        logger.info(f"Workout plan {workout_plan.id} created by user {current_user.get('username')}")
        return workout_plan
        
    except Exception as e:
        logger.error(f"Error creating workout plan: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workout plan"
        )


@router.get("/{planId}", response_model=WorkoutPlanResponse)
async def get_workout_plan(
    planId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get workout plan by ID"""
    workout_plan = db.query(WorkoutPlan).filter(WorkoutPlan.id == planId).first()
    if not workout_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout plan not found"
        )
    return workout_plan


@router.put("/{planId}", response_model=WorkoutPlanResponse)
async def update_workout_plan(
    planId: int,
    plan_update: WorkoutPlanUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update a workout plan"""
    workout_plan = db.query(WorkoutPlan).filter(WorkoutPlan.id == planId).first()
    if not workout_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout plan not found"
        )
    
    try:
        # Update workout plan fields
        update_data = plan_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(workout_plan, field, value)
        
        db.commit()
        db.refresh(workout_plan)
        
        logger.info(f"Workout plan {planId} updated by user {current_user.get('username')}")
        return workout_plan
        
    except Exception as e:
        logger.error(f"Error updating workout plan {planId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update workout plan"
        )


@router.delete("/{planId}")
async def delete_workout_plan(
    planId: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Delete a workout plan"""
    workout_plan = db.query(WorkoutPlan).filter(WorkoutPlan.id == planId).first()
    if not workout_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workout plan not found"
        )
    
    try:
        db.delete(workout_plan)
        db.commit()
        
        logger.info(f"Workout plan {planId} deleted by user {current_user.get('username')}")
        return {"message": "Workout plan deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting workout plan {planId}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete workout plan"
        )
