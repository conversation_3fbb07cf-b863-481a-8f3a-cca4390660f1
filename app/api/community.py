"""
Community API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import List, Optional
import logging

from ..core.database import get_db
from ..models.community import CommunityPost, PostLike, PostComment, UserFollow, Notification
from ..models.user import User, Friendship
from ..models.workout import Challenge, ChallengeParticipant
from .auth import get_current_user

router = APIRouter(prefix="/api/community", tags=["community"])
logger = logging.getLogger(__name__)


@router.get("/posts")
async def get_community_posts(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = Query(20, le=100),
    offset: int = Query(0, ge=0)
):
    """Get community posts for feed"""
    try:
        # Get posts from database with user information
        posts = db.query(CommunityPost).join(User).order_by(
            desc(CommunityPost.created_at)
        ).offset(offset).limit(limit).all()
        
        # Convert to response format
        posts_data = []
        for post in posts:
            # Check if current user liked this post
            user_liked = db.query(PostLike).filter(
                PostLike.post_id == post.id,
                PostLike.user_id == current_user["id"]
            ).first() is not None
            
            posts_data.append({
                "id": post.id,
                "user": {
                    "name": post.user.full_name,
                    "avatar": post.user.username[:2].upper() if post.user.username else post.user.full_name[:2].upper(),
                    "isVerified": post.user.is_verified
                },
                "timestamp": _format_timestamp(post.created_at),
                "content": post.content,
                "image": post.media_urls[0] if post.media_urls else None,
                "likes": post.likes_count,
                "comments": post.comments_count,
                "isLiked": user_liked,
                "type": post.post_type,
                "achievement": _get_achievement_data(post) if post.achievement_id else None,
                "workout": _get_workout_data(post) if post.workout_session_id else None,
                "challenge": _get_challenge_data(post) if post.challenge_id else None,
            })
        
        return {"posts": posts_data}
    except Exception as e:
        logger.error(f"Error fetching community posts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch community posts"
        )


@router.get("/challenges")
async def get_active_challenges(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = Query(10, le=50)
):
    """Get active challenges"""
    try:
        # Get active challenges from database
        challenges = db.query(Challenge).filter(
            Challenge.is_active == True
        ).order_by(desc(Challenge.created_at)).limit(limit).all()
        
        # Convert to response format
        challenges_data = []
        for challenge in challenges:
            # Check if current user is participating
            participation = db.query(ChallengeParticipant).filter(
                ChallengeParticipant.challenge_id == challenge.id,
                ChallengeParticipant.user_id == current_user["id"]
            ).first()
            
            # Get participant count
            participant_count = db.query(ChallengeParticipant).filter(
                ChallengeParticipant.challenge_id == challenge.id
            ).count()
            
            challenges_data.append({
                "id": challenge.id,
                "title": challenge.title,
                "description": challenge.description,
                "duration": f"{challenge.challenge_type}",
                "participants": f"{participant_count} participants",
                "difficulty": _get_difficulty_from_points(challenge.reward_points),
                "reward": f"{challenge.reward_points} points",
                "isJoined": participation is not None,
                "progress": participation.current_progress if participation else 0.0,
                "startDate": challenge.start_date.isoformat(),
                "endDate": challenge.end_date.isoformat(),
                "goalType": challenge.goal_type,
                "targetValue": challenge.target_value,
                "unit": challenge.unit
            })
        
        return {"challenges": challenges_data}
    except Exception as e:
        logger.error(f"Error fetching challenges: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch challenges"
        )


@router.get("/leaderboard")
async def get_leaderboard(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = Query(10, le=50)
):
    """Get weekly leaderboard"""
    try:
        # For now, calculate points based on achievements and challenge completions
        # This is a simplified leaderboard - in production you'd have a proper points system
        
        # Get users with their achievement counts and calculate points
        users_query = db.query(
            User.id,
            User.full_name,
            User.username,
            func.count(func.distinct(func.coalesce(func.nullif(ChallengeParticipant.id, None), 0))).label('challenge_completions')
        ).outerjoin(ChallengeParticipant, 
            (ChallengeParticipant.user_id == User.id) & 
            (ChallengeParticipant.is_completed == True)
        ).group_by(User.id, User.full_name, User.username).all()
        
        # Calculate points and create leaderboard
        leaderboard_data = []
        for user_data in users_query:
            # Simple point calculation: 100 points per completed challenge
            points = user_data.challenge_completions * 100
            
            leaderboard_data.append({
                "user_id": user_data.id,
                "name": user_data.full_name,
                "points": points,
                "avatar": user_data.username[:2].upper() if user_data.username else user_data.full_name[:2].upper(),
                "isMe": user_data.id == current_user["id"]
            })
        
        # Sort by points and add ranks
        leaderboard_data.sort(key=lambda x: x["points"], reverse=True)
        for i, user in enumerate(leaderboard_data[:limit]):
            user["rank"] = i + 1
        
        return {"leaderboard": leaderboard_data[:limit]}
    except Exception as e:
        logger.error(f"Error fetching leaderboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch leaderboard"
        )


@router.get("/friends")
async def get_user_friends(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's friends and friend suggestions"""
    try:
        # Get accepted friendships
        friends_query = db.query(Friendship, User).join(
            User, 
            (User.id == Friendship.friend_id) | (User.id == Friendship.user_id)
        ).filter(
            Friendship.status == "accepted",
            ((Friendship.user_id == current_user["id"]) | (Friendship.friend_id == current_user["id"])),
            User.id != current_user["id"]
        ).all()
        
        friends_data = []
        for friendship, friend in friends_query:
            friends_data.append({
                "id": friend.id,
                "name": friend.full_name,
                "avatar": friend.username[:2].upper() if friend.username else friend.full_name[:2].upper(),
                "isOnline": True,  # Mock data - implement real online status later
                "lastWorkout": "2 days ago",  # Mock data - implement real last workout tracking
                "mutualFriends": 0  # Mock data - implement mutual friends calculation
            })
        
        # Get friend suggestions (users not already friends)
        existing_friend_ids = [f.id for f in [friend for _, friend in friends_query]]
        existing_friend_ids.append(current_user["id"])
        
        suggestions = db.query(User).filter(
            ~User.id.in_(existing_friend_ids)
        ).limit(5).all()
        
        suggestions_data = []
        for user in suggestions:
            suggestions_data.append({
                "id": user.id,
                "name": user.full_name,
                "avatar": user.username[:2].upper() if user.username else user.full_name[:2].upper(),
                "mutualFriends": 0  # Mock data
            })
        
        return {
            "friends": friends_data,
            "suggestions": suggestions_data
        }
    except Exception as e:
        logger.error(f"Error fetching friends: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch friends"
        )


# Helper functions
def _format_timestamp(dt):
    """Format datetime to relative time string"""
    # Simple implementation - in production use a proper library
    return "2 hours ago"  # Mock for now


def _get_achievement_data(post):
    """Get achievement data for post"""
    if post.achievement:
        return {
            "title": "Achievement Unlocked",
            "icon": "emoji_events"  # Mock icon
        }
    return None


def _get_workout_data(post):
    """Get workout data for post"""
    if post.workout_session:
        return {
            "name": "Workout Session",
            "duration": "45 min"  # Mock data
        }
    return None


def _get_challenge_data(post):
    """Get challenge data for post"""
    if post.challenge:
        return {
            "name": post.challenge.title,
            "participants": "50"  # Mock data
        }
    return None


def _get_difficulty_from_points(points):
    """Convert reward points to difficulty level"""
    if points < 100:
        return "Beginner"
    elif points < 300:
        return "Intermediate"
    elif points < 500:
        return "Advanced"
    else:
        return "Expert"
