"""
Gym-related database models
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, <PERSON><PERSON><PERSON>, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum

from ..core.database import Base


class UserRole(PyEnum):
    ADMIN = "admin"
    TRAINER = "trainer"
    MEMBER = "member"


class Gym(Base):
    """Gym model"""
    __tablename__ = "gyms"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    address = Column(Text)
    phone = Column(String(20))
    email = Column(String(255))
    website = Column(String(255))
    opening_hours = Column(Text)  # JSON string for opening hours
    amenities = Column(Text)  # JSON string for amenities list
    membership_fees = Column(Text)  # JSON string for different membership types and fees
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    admins = relationship("GymAdmin", back_populates="gym", cascade="all, delete-orphan")
    trainers = relationship("GymTrainer", back_populates="gym", cascade="all, delete-orphan")
    members = relationship("GymMember", back_populates="gym", cascade="all, delete-orphan")
    store_inventory = relationship("GymStoreInventory", back_populates="gym", cascade="all, delete-orphan")


class GymAdmin(Base):
    """Gym admin association model"""
    __tablename__ = "gym_admins"

    id = Column(Integer, primary_key=True, index=True)
    gym_id = Column(Integer, ForeignKey("gyms.id"), nullable=False)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    gym = relationship("Gym", back_populates="admins")
    user = relationship("User")


class GymTrainer(Base):
    """Gym trainer association model"""
    __tablename__ = "gym_trainers"

    id = Column(Integer, primary_key=True, index=True)
    gym_id = Column(Integer, ForeignKey("gyms.id"), nullable=False)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    specializations = Column(Text)  # JSON string for trainer specializations
    certification = Column(String(255))
    experience_years = Column(Integer)
    hourly_rate = Column(Float)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    gym = relationship("Gym", back_populates="trainers")
    user = relationship("User")


class GymMember(Base):
    """Gym member association model"""
    __tablename__ = "gym_members"

    id = Column(Integer, primary_key=True, index=True)
    gym_id = Column(Integer, ForeignKey("gyms.id"), nullable=False)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    membership_type = Column(String(100))  # basic, premium, vip, etc.
    membership_start = Column(DateTime(timezone=True))
    membership_end = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    gym = relationship("Gym", back_populates="members")
    user = relationship("User")


class GymStoreInventory(Base):
    """Gym store inventory model"""
    __tablename__ = "gym_store_inventory"
    
    id = Column(Integer, primary_key=True, index=True)
    gym_id = Column(Integer, ForeignKey("gyms.id"), nullable=False)
    product_name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100))  # supplements, equipment, apparel, etc.
    brand = Column(String(100))
    price = Column(Float, nullable=False)
    stock_quantity = Column(Integer, default=0)
    sku = Column(String(100), unique=True)
    image_url = Column(String(500))
    is_available = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    gym = relationship("Gym", back_populates="store_inventory")
