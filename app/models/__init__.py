"""
Database models for WibeFit Backend
"""

# Import all models to ensure they are registered with SQLAlchemy
from .user import (
    User,
    UserProfile,
    UserMeasurement,
    UserGoal,
    UserAchievement,
    Friendship
)

from .exercise import (
    Exercise,
    ExerciseVariation,
    UserExercise,
    ExerciseLog,
    MuscleGroup,
    Equipment
)

from .workout import (
    WorkoutPlan,
    WorkoutExercise,
    WorkoutSession,
    UserWorkout,
    Challenge,
    ChallengeParticipant
)

from .community import (
    CommunityPost,
    PostLike,
    PostComment,
    PostShare,
    UserFollow,
    Group,
    GroupMember,
    GroupPost,
    Notification,
    Report
)

# Export all models
__all__ = [
    # User models
    "User",
    "UserProfile", 
    "UserMeasurement",
    "UserGoal",
    "UserAchievement",
    "Friendship",
    
    # Exercise models
    "Exercise",
    "ExerciseVariation",
    "UserExercise", 
    "ExerciseLog",
    "MuscleGroup",
    "Equipment",
    
    # Workout models
    "WorkoutPlan",
    "WorkoutExercise",
    "WorkoutSession",
    "UserWorkout",
    "Challenge",
    "ChallengeParticipant",
    
    # Community models
    "CommunityPost",
    "PostLike",
    "PostComment", 
    "PostShare",
    "UserFollow",
    "Group",
    "GroupMember",
    "GroupPost",
    "Notification",
    "Report"
]
