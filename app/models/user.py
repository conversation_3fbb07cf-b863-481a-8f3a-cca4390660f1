"""
User database models
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from ..core.database import Base


class User(Base):
    __tablename__ = "users"

    username = Column(String(100), primary_key=True, index=True)
    email = Column(String(255), index=True, nullable=True)  # Email is optional and editable
    full_name = Column(String(255), nullable=False)
    hashed_password = Column(String(255), nullable=True)  # Optional for Firebase users
    role = Column(String(50), default="member")  # member, trainer, admin
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    profile_picture = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    date_of_birth = Column(DateTime, nullable=True)
    phone_number = Column(String(20), nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    user_workouts = relationship("UserWorkout", back_populates="user")
    workout_sessions = relationship("WorkoutSession", back_populates="user")
    measurements = relationship("UserMeasurement", back_populates="user")
    goals = relationship("UserGoal", back_populates="user")
    achievements = relationship("UserAchievement", back_populates="user")
    posts = relationship("CommunityPost", back_populates="user")
    friendships_sent = relationship("Friendship", foreign_keys="Friendship.username", back_populates="user")
    friendships_received = relationship("Friendship", foreign_keys="Friendship.friend_username", back_populates="friend")


class UserProfile(Base):
    __tablename__ = "user_profiles"

    username = Column(String(100), ForeignKey("users.username"), primary_key=True)
    age = Column(Integer, nullable=True)
    phone = Column(String(20), nullable=True)  # Added phone field from database
    gender = Column(String(20), nullable=True)  # Added gender field
    height = Column(Float, nullable=True)  # in cm
    current_weight = Column(Float, nullable=True)  # in kg
    target_weight = Column(Float, nullable=True)  # in kg
    fitness_level = Column(String(50), nullable=True)  # beginner, intermediate, advanced, expert
    activity_level = Column(String(50), nullable=True)  # sedentary, lightly_active, moderately_active, very_active
    primary_goals = Column(JSON, nullable=True)  # List of goals
    medical_conditions = Column(Text, nullable=True)
    dietary_restrictions = Column(JSON, nullable=True)  # List of restrictions
    preferred_workout_duration = Column(Integer, nullable=True)  # in minutes
    preferred_workout_days = Column(JSON, nullable=True)  # List of days
    equipment_access = Column(JSON, nullable=True)  # List of available equipment
    profile_setup_completed = Column(Boolean, default=False)  # Added field from database
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="profile")


class UserMeasurement(Base):
    __tablename__ = "user_measurements"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    measurement_type = Column(String(50), nullable=False)  # weight, body_fat, muscle_mass, etc.
    value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=False)  # kg, %, cm, etc.
    notes = Column(Text, nullable=True)
    measured_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="measurements")


class UserGoal(Base):
    __tablename__ = "user_goals"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    goal_type = Column(String(50), nullable=False)  # weight_loss, muscle_gain, endurance, etc.
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    target_value = Column(Float, nullable=True)
    current_value = Column(Float, default=0.0)
    unit = Column(String(20), nullable=True)
    target_date = Column(DateTime, nullable=True)
    is_completed = Column(Boolean, default=False)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="goals")


class UserAchievement(Base):
    __tablename__ = "user_achievements"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    achievement_type = Column(String(50), nullable=False)  # workout_streak, weight_milestone, etc.
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    icon = Column(String(100), nullable=True)
    points = Column(Integer, default=0)
    earned_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="achievements")


class Friendship(Base):
    __tablename__ = "friendships"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    friend_username = Column(String(100), ForeignKey("users.username"), nullable=False)
    status = Column(String(20), default="pending")  # pending, accepted, blocked
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", foreign_keys="Friendship.username", back_populates="friendships_sent")
    friend = relationship("User", foreign_keys="Friendship.friend_username", back_populates="friendships_received")
