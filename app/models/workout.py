"""
Workout database models
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..core.database import Base


class WorkoutPlan(Base):
    __tablename__ = "workout_plans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    slug = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    creator_username = Column(String(100), ForeignKey("users.username"), nullable=True)
    difficulty = Column(String(50), nullable=False)  # beginner, intermediate, advanced, expert
    duration_minutes = Column(Integer, nullable=False)
    category = Column(String(100), nullable=False)  # strength, cardio, flexibility, mixed
    target_muscle_groups = Column(JSON, nullable=True)  # Primary muscle groups targeted
    equipment_needed = Column(JSON, nullable=True)  # Required equipment
    calories_estimate = Column(Float, nullable=True)  # Estimated calories burned
    is_public = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    rating = Column(Float, default=0.0)  # Average user rating
    total_ratings = Column(Integer, default=0)
    total_completions = Column(Integer, default=0)
    tags = Column(JSON, nullable=True)  # Tags for filtering
    thumbnail_url = Column(String(500), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    creator = relationship("User")
    exercises = relationship("WorkoutExercise", back_populates="workout_plan")
    sessions = relationship("WorkoutSession", back_populates="workout_plan")
    user_workouts = relationship("UserWorkout", back_populates="workout_plan")


class WorkoutExercise(Base):
    __tablename__ = "workout_exercises"

    id = Column(Integer, primary_key=True, index=True)
    workout_plan_id = Column(Integer, ForeignKey("workout_plans.id"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False)
    order_index = Column(Integer, nullable=False)  # Order in the workout
    sets = Column(Integer, nullable=True)
    reps = Column(JSON, nullable=True)  # Can be a number or range [8, 12]
    weight_kg = Column(Float, nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    rest_seconds = Column(Integer, nullable=True)
    distance_meters = Column(Float, nullable=True)
    notes = Column(Text, nullable=True)
    is_warmup = Column(Boolean, default=False)
    is_cooldown = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    workout_plan = relationship("WorkoutPlan", back_populates="exercises")
    exercise = relationship("Exercise", back_populates="workout_exercises")


class WorkoutSession(Base):
    __tablename__ = "workout_sessions"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    workout_plan_id = Column(Integer, ForeignKey("workout_plans.id"), nullable=True)
    name = Column(String(255), nullable=True)  # For custom workouts
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    paused_at = Column(DateTime(timezone=True), nullable=True)
    duration_minutes = Column(Float, nullable=True)
    calories_burned = Column(Float, nullable=True)
    average_heart_rate = Column(Integer, nullable=True)
    max_heart_rate = Column(Integer, nullable=True)
    difficulty_rating = Column(Integer, nullable=True)  # 1-10 scale
    satisfaction_rating = Column(Integer, nullable=True)  # 1-10 scale
    notes = Column(Text, nullable=True)
    weather = Column(String(100), nullable=True)  # For outdoor workouts
    location = Column(String(255), nullable=True)
    is_completed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="workout_sessions")
    workout_plan = relationship("WorkoutPlan", back_populates="sessions")
    exercise_logs = relationship("ExerciseLog", back_populates="workout_session")


class UserWorkout(Base):
    __tablename__ = "user_workouts"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    workout_plan_id = Column(Integer, ForeignKey("workout_plans.id"), nullable=False)
    is_favorite = Column(Boolean, default=False)
    times_completed = Column(Integer, default=0)
    best_time_minutes = Column(Float, nullable=True)
    last_completed = Column(DateTime, nullable=True)
    personal_rating = Column(Integer, nullable=True)  # 1-10 scale
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="user_workouts")
    workout_plan = relationship("WorkoutPlan", back_populates="user_workouts")


class Challenge(Base):
    __tablename__ = "challenges"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    challenge_type = Column(String(50), nullable=False)  # daily, weekly, monthly, custom
    goal_type = Column(String(50), nullable=False)  # workouts, calories, distance, time
    target_value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    reward_points = Column(Integer, default=0)
    reward_badge = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    created_by = Column(String(100), ForeignKey("users.username"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    creator = relationship("User")
    participants = relationship("ChallengeParticipant", back_populates="challenge")


class ChallengeParticipant(Base):
    __tablename__ = "challenge_participants"

    id = Column(Integer, primary_key=True, index=True)
    challenge_id = Column(Integer, ForeignKey("challenges.id"), nullable=False)
    username = Column(String(100), ForeignKey("users.username"), nullable=False)
    current_progress = Column(Float, default=0.0)
    is_completed = Column(Boolean, default=False)
    completed_at = Column(DateTime, nullable=True)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    challenge = relationship("Challenge", back_populates="participants")
    user = relationship("User")
