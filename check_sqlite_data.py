import sqlite3
import json

DB_FILE = "wibefit.db"

def check_database():
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("📋 Tables:", [table[0] for table in tables])
        
        # Check users table
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👥 Users count: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT * FROM users LIMIT 5")
            users = cursor.fetchall()
            print("👤 Sample users:", users)
        
        conn.close()
    except Exception as e:
        print(f"❌ Database check error: {e}")

if __name__ == "__main__":
    check_database()