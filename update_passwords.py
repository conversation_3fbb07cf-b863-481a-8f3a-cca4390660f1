#!/usr/bin/env python3
"""
Update existing users with password hashes
"""

import psycopg2
import hashlib
import secrets

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'wibefit_db',
    'user': 'sanju',
    'password': '0810',
    'port': 5432
}

def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
    return f"{salt}:{password_hash}"

def update_user_passwords():
    """Update existing users with password hashes"""
    
    # Users to update with their passwords
    users_to_update = [
        ('demo', 'demo123'),
        ('demouser', 'demo123'),
        ('admin', 'admin123'),
        ('sanju', '123456'),  # Simple 6-character password
        ('sanjay', 'sanjay'),  # 6-character password
        ('newuser', 'newuser'),  # 7-character password
    ]
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        for username, password in users_to_update:
            # Check if user exists
            cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                # Hash the password
                password_hash = hash_password(password)

                # Update the user (force update even if password exists)
                cursor.execute(
                    "UPDATE users SET password_hash = %s WHERE username = %s",
                    (password_hash, username)
                )
                print(f"✅ Updated password for user: {username}")
            else:
                print(f"⚠️ User {username} not found")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("✅ Password update completed!")
        
    except Exception as e:
        print(f"❌ Error updating passwords: {e}")

if __name__ == "__main__":
    update_user_passwords()
