-- WibeFit Database Schema
-- This file contains the complete database schema for the WibeFit application
-- Run this file to create all necessary tables and initial data

-- ============================================================================
-- USERS TABLE
-- ============================================================================
-- Core user authentication and basic information
CREATE TABLE IF NOT EXISTS users (
    username VARCHAR(50) PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    full_name VARCHAR(255),
    hashed_password VARCHAR(255),
    role VARCHAR(20) DEFAULT 'member',
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    profile_picture TEXT,
    bio TEXT,
    phone_number VARCHAR(20),
    timezone VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- USER PROFILES TABLE
-- ============================================================================
-- Detailed fitness profiles and preferences
CREATE TABLE IF NOT EXISTS user_profiles (
    username VARCHAR(50) PRIMARY KEY REFERENCES users(username) ON DELETE CASCADE,
    age INTEGER,
    gender VARCHAR(20),
    height DECIMAL(5,2), -- in cm
    current_weight DECIMAL(5,2), -- in kg
    target_weight DECIMAL(5,2), -- in kg
    fitness_level VARCHAR(20), -- beginner, intermediate, advanced
    activity_level VARCHAR(20), -- sedentary, light, moderate, active, very_active
    primary_goals TEXT[], -- array of goals
    equipment_access TEXT[], -- array of available equipment
    preferred_workout_days TEXT[], -- array of preferred days
    preferred_workout_duration INTEGER, -- in minutes
    medical_conditions TEXT[],
    injuries TEXT[],
    profile_setup_completed BOOLEAN DEFAULT FALSE,
    profile_setup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Additional contact info
    email VARCHAR(255),
    phone VARCHAR(20),
    date_of_birth DATE
);

-- ============================================================================
-- EXERCISES TABLE
-- ============================================================================
-- Exercise database with instructions and media
CREATE TABLE IF NOT EXISTS exercises (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    instructions TEXT[],
    tips TEXT[],
    muscle_groups TEXT[] NOT NULL,
    equipment TEXT[],
    difficulty VARCHAR(20) NOT NULL, -- beginner, intermediate, advanced
    exercise_type VARCHAR(50) NOT NULL, -- strength, cardio, flexibility, etc.
    category VARCHAR(50) NOT NULL,
    duration_minutes INTEGER,
    calories_per_minute DECIMAL(4,2),
    video_url TEXT,
    image_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Google Drive integration
    drive_video_id VARCHAR(255),
    original_filename VARCHAR(255),
    drive_video_url TEXT,
    drive_thumbnail_url TEXT
);

-- ============================================================================
-- WORKOUT SESSIONS TABLE
-- ============================================================================
-- Track completed workouts
CREATE TABLE IF NOT EXISTS workout_sessions (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) REFERENCES users(username) ON DELETE CASCADE,
    workout_name VARCHAR(255),
    workout_type VARCHAR(50),
    target_muscle_groups TEXT[],
    duration_minutes INTEGER,
    calories_burned INTEGER,
    exercises_completed INTEGER,
    total_exercises INTEGER,
    difficulty_level VARCHAR(20),
    completion_percentage DECIMAL(5,2),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Workout data
    workout_data JSONB, -- Store detailed workout information
    notes TEXT
);

-- ============================================================================
-- USER STATS TABLE
-- ============================================================================
-- User fitness statistics and progress
CREATE TABLE IF NOT EXISTS user_stats (
    username VARCHAR(50) PRIMARY KEY REFERENCES users(username) ON DELETE CASCADE,
    total_workouts INTEGER DEFAULT 0,
    total_duration_minutes INTEGER DEFAULT 0,
    total_calories_burned INTEGER DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    favorite_exercise_type VARCHAR(50),
    average_workout_duration DECIMAL(5,2),
    last_workout_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_exercises_muscle_groups ON exercises USING GIN(muscle_groups);
CREATE INDEX IF NOT EXISTS idx_exercises_category ON exercises(category);
CREATE INDEX IF NOT EXISTS idx_exercises_difficulty ON exercises(difficulty);
CREATE INDEX IF NOT EXISTS idx_workout_sessions_username ON workout_sessions(username);
CREATE INDEX IF NOT EXISTS idx_workout_sessions_completed_at ON workout_sessions(completed_at);

-- ============================================================================
-- SAMPLE DATA
-- ============================================================================

-- Insert demo user
INSERT INTO users (username, email, full_name, hashed_password, role) 
VALUES ('demo', '<EMAIL>', 'Demo User', 'demo123', 'member')
ON CONFLICT (username) DO NOTHING;

-- Insert demo user profile
INSERT INTO user_profiles (
    username, age, gender, height, current_weight, fitness_level, 
    activity_level, primary_goals, equipment_access, preferred_workout_days,
    preferred_workout_duration, profile_setup_completed, email
) VALUES (
    'demo', 25, 'other', 175.0, 70.0, 'intermediate', 'moderate',
    ARRAY['Weight Loss', 'Strength'], ARRAY['Dumbbells', 'Resistance Bands'],
    ARRAY['Monday', 'Wednesday', 'Friday'], 45, TRUE, '<EMAIL>'
) ON CONFLICT (username) DO NOTHING;

-- Insert sample exercises
INSERT INTO exercises (name, slug, description, muscle_groups, equipment, difficulty, exercise_type, category, duration_minutes, calories_per_minute) VALUES
('Push-ups', 'push-ups', 'Classic bodyweight chest exercise', ARRAY['Chest', 'Triceps', 'Shoulders'], ARRAY['None'], 'beginner', 'strength', 'Upper Body', 3, 8.0),
('Squats', 'squats', 'Fundamental lower body exercise', ARRAY['Quadriceps', 'Glutes', 'Hamstrings'], ARRAY['None'], 'beginner', 'strength', 'Lower Body', 3, 6.0),
('Plank', 'plank', 'Core strengthening isometric exercise', ARRAY['Core', 'Shoulders'], ARRAY['None'], 'beginner', 'strength', 'Core', 2, 4.0),
('Jumping Jacks', 'jumping-jacks', 'Full body cardio exercise', ARRAY['Full Body'], ARRAY['None'], 'beginner', 'cardio', 'Cardio', 5, 10.0),
('Dumbbell Bicep Curls', 'dumbbell-bicep-curls', 'Isolated bicep strengthening', ARRAY['Biceps'], ARRAY['Dumbbells'], 'intermediate', 'strength', 'Upper Body', 3, 5.0)
ON CONFLICT (slug) DO NOTHING;

-- Initialize user stats for demo user
INSERT INTO user_stats (username, total_workouts, total_duration_minutes, total_calories_burned)
VALUES ('demo', 0, 0, 0)
ON CONFLICT (username) DO NOTHING;

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_exercises_updated_at ON exercises;
CREATE TRIGGER update_exercises_updated_at BEFORE UPDATE ON exercises FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_stats_updated_at ON user_stats;
CREATE TRIGGER update_user_stats_updated_at BEFORE UPDATE ON user_stats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
