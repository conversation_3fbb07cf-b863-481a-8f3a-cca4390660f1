#!/usr/bin/env python3
"""
Create user favorites table for exercises
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import settings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_favorites_table():
    """Create user_exercise_favorites table"""
    try:
        engine = create_engine(settings.database_url)
        
        # SQL to create user exercise favorites table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user_exercise_favorites (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            exercise_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (exercise_id) REFERENCES exercises(id) ON DELETE CASCADE,
            UNIQUE(user_id, exercise_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_user_exercise_favorites_user_id ON user_exercise_favorites(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_exercise_favorites_exercise_id ON user_exercise_favorites(exercise_id);
        """
        
        with engine.connect() as conn:
            conn.execute(text(create_table_sql))
            conn.commit()
        
        logger.info("✅ User exercise favorites table created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating favorites table: {e}")
        return False

def create_recent_exercises_table():
    """Create user recent exercises table"""
    try:
        engine = create_engine(settings.database_url)
        
        # SQL to create user recent exercises table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user_recent_exercises (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            exercise_id INTEGER NOT NULL,
            accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (exercise_id) REFERENCES exercises(id) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS idx_user_recent_exercises_user_id ON user_recent_exercises(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_recent_exercises_accessed_at ON user_recent_exercises(accessed_at);
        """
        
        with engine.connect() as conn:
            conn.execute(text(create_table_sql))
            conn.commit()
        
        logger.info("✅ User recent exercises table created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating recent exercises table: {e}")
        return False

def main():
    """Create all exercise-related tables"""
    logger.info("🚀 Creating exercise enhancement tables...")
    
    success1 = create_favorites_table()
    success2 = create_recent_exercises_table()
    
    if success1 and success2:
        logger.info("🎉 All exercise enhancement tables created successfully!")
        logger.info("Next steps:")
        logger.info("1. Restart backend server")
        logger.info("2. Test favorites functionality")
        logger.info("3. Test recent exercises tracking")
    else:
        logger.error("❌ Some tables failed to create")

if __name__ == "__main__":
    main()
