#!/usr/bin/env python3
"""
Add comprehensive exercise list to the database
Includes 425 exercises: 219 Abdominal + 206 Back exercises
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import Session<PERSON>ocal
from app.models.exercise import Exercise
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_slug(name, session=None, counter=0):
    """Create URL-friendly slug from exercise name"""
    base_slug = name.lower().replace(' ', '-').replace('(', '').replace(')', '').replace('_', '-').replace('--', '-')

    # Remove any trailing dashes
    base_slug = base_slug.strip('-')

    # If counter is provided, append it
    if counter > 0:
        slug = f"{base_slug}-{counter}"
    else:
        slug = base_slug

    # Check for duplicates if session is provided
    if session:
        existing = session.query(Exercise).filter_by(slug=slug).first()
        if existing:
            return create_slug(name, session, counter + 1)

    return slug

def categorize_exercise(name, category_type):
    """Categorize exercise based on name and type"""
    name_lower = name.lower()
    
    if category_type == "ABDOMINALS":
        # Determine subcategory for abdominal exercises
        if any(word in name_lower for word in ['plank', 'side plank']):
            return 'core', ['Core', 'Shoulders'], 'intermediate'
        elif any(word in name_lower for word in ['crunch', 'sit-up', 'situp']):
            return 'core', ['Core', 'Abs'], 'beginner'
        elif any(word in name_lower for word in ['wheel', 'rollout']):
            return 'core', ['Core', 'Shoulders'], 'advanced'
        elif any(word in name_lower for word in ['twist', 'oblique']):
            return 'core', ['Core', 'Obliques'], 'intermediate'
        elif any(word in name_lower for word in ['leg raise', 'knee raise']):
            return 'core', ['Core', 'Hip Flexors'], 'intermediate'
        else:
            return 'core', ['Core'], 'beginner'
    
    elif category_type == "BACK":
        # Determine subcategory for back exercises
        if any(word in name_lower for word in ['deadlift']):
            return 'full_body', ['Back', 'Glutes', 'Hamstrings'], 'intermediate'
        elif any(word in name_lower for word in ['pull-up', 'pullup', 'chin-up', 'chinup']):
            return 'upper_body', ['Back', 'Biceps'], 'advanced'
        elif any(word in name_lower for word in ['row', 'pulldown']):
            return 'upper_body', ['Back', 'Biceps'], 'intermediate'
        elif any(word in name_lower for word in ['superman', 'extension']):
            return 'upper_body', ['Back', 'Glutes'], 'beginner'
        else:
            return 'upper_body', ['Back'], 'intermediate'

def determine_equipment(name):
    """Determine equipment needed based on exercise name"""
    name_lower = name.lower()
    
    if any(word in name_lower for word in ['barbell']):
        return ['Barbell']
    elif any(word in name_lower for word in ['dumbbell', 'dumbbells']):
        return ['Dumbbells']
    elif any(word in name_lower for word in ['cable']):
        return ['Cable Machine']
    elif any(word in name_lower for word in ['kettlebell']):
        return ['Kettlebell']
    elif any(word in name_lower for word in ['band', 'resistance']):
        return ['Resistance Band']
    elif any(word in name_lower for word in ['machine']):
        return ['Machine']
    elif any(word in name_lower for word in ['ball', 'stability']):
        return ['Exercise Ball']
    elif any(word in name_lower for word in ['suspension', 'trx']):
        return ['Suspension Trainer']
    elif any(word in name_lower for word in ['bench']):
        return ['Bench']
    elif any(word in name_lower for word in ['ab wheel', 'wheel']):
        return ['Ab Wheel']
    else:
        return ['None']

def determine_difficulty(name, base_difficulty):
    """Determine difficulty based on exercise name and base difficulty"""
    name_lower = name.lower()
    
    # Advanced exercises
    if any(word in name_lower for word in ['dragonfly', 'muscle-up', 'archer', 'commando']):
        return 'expert'
    elif any(word in name_lower for word in ['wheel', 'rollout', 'pull-up', 'chin-up']):
        return 'advanced'
    elif any(word in name_lower for word in ['assisted', 'machine']):
        return 'beginner'
    else:
        return base_difficulty

# Exercise data
ABDOMINAL_EXERCISES = [
    "3-4 Sit-up_female", "45 degree bicycle twist knee to elbow", "45 degree Bicycle Twisting Crunch_female",
    "45 Degree Bicycle Twisting_female", "45 degree hyperextension arms to chest", "45 degree side bend",
    "45 Degree Side Bend_female", "45 degree twisting hyperextension", "45 degree twisting hyperextension_female",
    "90 degree heel touch", "90 Degree Heels Touch_female", "Ab Mat Sit-up_female", "Ab Tuck_female",
    "AB Wheel All The Way out", "AB Wheel All The Way out_Female", "AB Wheel Halfway out", "AB Wheel Halfway out_Female",
    "AB Wheel Left out", "AB Wheel Left out_Female", "AB Wheel Pulses", "AB Wheel Pulses_Female",
    "AB Wheel Right out", "AB Wheel Right out_Female", "AB Wheel Take it or Leave It", "AB Wheel Take it or Leave It_Female",
    "abdominal crunches machine", "abdominal crunches with hold", "abdominal crunches", "Abs Snails", "Abs Snails_Female",
    "Air Bike (VERSION 2)_female", "air bike crunch", "air bike_female", "air twisting crunch", "air twisting crunch_female",
    "alternate arm leg plank hold", "alternate arm leg plank rest pause", "Alternate Arm Leg Plank", "Alternate heel touches",
    "Alternate leg pull", "Alternate Leg Pull_female", "Alternate leg raise from reverse plank position",
    "Alternate Leg Raise from Reverse Plank Position_female", "Alternate leg raise with head up", "Alternate Leg Raise with Head",
    "Alternate leg raise", "Alternate Leg Raise_female", "Alternate lying floor leg raise", "Alternate Lying Floor Leg Raise_female",
    "Alternate oblique crunch", "Alternate Oblique Crunch_female", "alternate single leg raises plank", "Alternate toe tap leg lift",
    "Alternate Toe Tap Leg Lift_female", "alternating leg lifts", "alternating oblique sit ups", "Alternating Plank Lunge",
    "alternating toe tap", "Arm rotation knee lift", "Assisted sit ups", "Assisted Sit-Up_female", "Ball Sit-up_female",
    "Band Bicycle Crunch", "Band Bicycle Crunch_female", "Band Decline Sit up_female", "band kneeling crunch_female",
    "Band side bend_female", "Barbell Landmine Side Bend", "Barbell Landmine Side Bend_Female", "Barbell rollout",
    "barbell seated twist on exercise ball", "Barbell seated twist", "Barbell side bends", "Barbell Situp", "Barbell Situp_Female",
    "Barbell standing twist", "bench decline ab sit-ups", "bench knee tucks", "Bent knee Lying Twist (On Stability Ball)",
    "Bent knee Lying Twist", "bicycle twisting crunch", "bicycles crunches", "bicycles", "Bottoms-Up half rep",
    "brazillian crunches", "Bridge - Mountain Climber (Cross Body)", "Cable Standing Crunch", "Cable Standing Crunch_Female",
    "Cable twist (up-down)", "Cable Twist", "Captains chair leg raise", "Captains Chair Straight Leg Raise",
    "chest lift with rotation", "Crab Twist Toe Touch", "Criss Cross Leg Raises", "Crunch (arms straight)",
    "Crunch (hands overhead)", "Crunch (legs on stability ball)", "Crunch (on bosu ball)", "Crunch (on stability ball)",
    "Crunch (straight leg up)", "Crunch Floor", "Crunch Frog on Floor", "Crunch leg raise", "Crunch with Medicine Ball",
    "dead bug", "Dead Bug Extended Arms", "Decline Bench Full Sit up", "decline bench oblique crunches bodyweight",
    "decline bench oblique crunches dumbbells", "Decline Bent Leg Reverse Crunch", "decline levitating sit ups bodyweight",
    "decline sit up", "decline sit ups dumbbells", "diagonal chop cable", "diagonal chop left", "diagonal chop right",
    "Dragonfly", "Dragonfly_Female", "dumbbell ab crunch", "Dumbbell Crunch Hold with Legs Off", "Dumbbell Decline Overhead Sit-up",
    "Dumbbell Decline Sit-up", "dumbbell diagonal chop", "Dumbbell Elbow Side Plank", "Dumbbell Elbow Side Plank_Female",
    "Dumbbell Half Kneeling Wood Chopper", "Dumbbell Half Kneeling Wood Chopper_Female", "Dumbbell Hand Side Plank",
    "Dumbbell Hand Side Plank_Female", "Dumbbell Kneeling Wood Chopper", "Dumbbell Kneeling Wood Chopper_Female",
    "Dumbbell Overhead Side Bend", "Dumbbell Overhead Side Bend_Female", "Dumbbell Overhead Sit-up",
    "Dumbbell Russian Twist with Legs Floor Off", "Dumbbell Side Bend", "Dumbbell Side Plank Up Down",
    "Dumbbell Side Plank Up Down_Female", "Dumbbell Side Plank with Rear Fly", "Dumbbell Single Arm Starfish Crunch",
    "Dumbbell Starfish Crunch Alternating", "Dumbbell Straight Arm Crunch", "Dumbbell Straight Arm Twisting Sit-up",
    "Dumbbell Straight Leg Russian Twist", "Dumbbell Suitcase Crunch", "Dumbbell Suitcase Crunch_Female", "Dumbbell V-up",
    "Elbow Push Plank Up", "Elbow-Up and Down Dynamic Plank", "Exercise Ball Body Saw", "exercise ball sit ups",
    "flutter kicks", "hanging knee raises", "hanging oblique crunches", "heel touches", "high cable kneeling crunch",
    "high knee twist", "high resistance band kneeling crunch", "hollow hold", "jack knives", "Kettlebell situp press",
    "leg lift circles", "Leg raises (straight legs)", "long arm crunches", "lying abs resistance band", "lying leg raise",
    "middle crunches", "mountain climbers", "oblique crunch", "over head weight sit up", "over weight ab crunch",
    "plank cross knee drive", "plank jack", "Plank Knee Tucks", "plank on elbows", "plank reach through",
    "raised leg crunch", "Resistance Band Lying Bent Knee Raise", "Reverse Ab Wheel Rollout", "Reverse Ab Wheel Rollout_Female",
    "reverse crunch", "reverse crunch with kick out", "russian twist", "russian twist weighted ball", "scissors kick",
    "Seated floor crunches", "seated flutter kicks", "seated v up", "side plank oblique crunch", "side plank", "Situps",
    "spider plank", "Standing Ab Wheel Rollout", "Standing Ab Wheel Rollout_Female", "standing cable oblique twist",
    "standing oblique crunch", "star crunches", "Suspension Trainer with Grips Abdominal Fallout",
    "Suspension Trainer with Grips Abdominal Fallout_female", "Suspension Trainer with Grips Hanging Knees to Elbows",
    "Suspension Trainer with Grips Hanging Leg Hip Raise", "Suspension Trainer with Grips Hanging Leg Hip Raise_female",
    "Suspension Trainer with Grips Hanging Straight Leg Hip Raise", "Suspension Trainer with Grips Hanging Straight Leg Hip Raise_female",
    "Suspension Trainer with Grips Pull Through", "Suspension Trainer with Grips Pull Through_female",
    "Suspension Trainer with Grips Reverse Ab Rollout", "Suspension Trainer with Grips Reverse Ab Rollout_female",
    "Suspension Trainer with Grips Supine Crunch", "Suspension Trainer with Grips Supine Crunch_female", "V crunches",
    "v up", "walkout", "Wheels To Heaven", "Wheels To Heaven_Female", "Wheels To Toes", "Wheels To Toes_Female", "wind wipers"
]

BACK_EXERCISES = [
    "45 degree hyperextension (arms in front of chest)_female", "alternate superman", "Alternating Superman_female",
    "Archer pull up", "around the world superman hold", "around the world superman hold_female",
    "assisted chin up normal width reverse grip", "assisted chin up reverse close grip", "assisted chin up reverse wide grip",
    "Assisted Chin-up on a bench_female", "Assisted close grip underhand chin up", "Assisted Close-grip Underhand Chin-up_female",
    "Assisted parallel close grip pull up", "Assisted Parallel Close-Grip Pull-up_female", "assisted pull up close grip",
    "assisted pull up normal grip", "assisted pull up wide grip", "Assisted pull up", "Assisted Pull-up_female",
    "back extension machine", "Back extension on exercise ball", "Back Extension on Exercise Ball_female", "Back squeeze",
    "Band Assisted Chin Up (From Knee)_female", "Band bent-over row", "Band bent-over row_female", "Band Deadlift",
    "Band Fixed Back Underhand Pulldown_female", "Band Hammer Grip Incline Bench Two Arm Row_female",
    "band kneeling lat pulldown_female", "Band Kneeling Single Arm High Row", "Band Kneeling Single Arm High Row_Female",
    "Band Lying Reverse Grip Row_female", "Band Pull Up", "Band Pull Up_Female", "Barbell bent over row pronated grip",
    "Barbell bent over row supinated grip", "Barbell Bent Over Row_female", "Barbell clean deadlift", "Barbell Coan Deadlift",
    "Barbell Coan Deadlift_Female", "Barbell deadlift 360 degrees", "Barbell Deadlift High Pull", "Barbell Deadlift High Pull_Female",
    "Barbell Good Morning", "Barbell Long Landmine Row", "Barbell Lying Row", "Barbell Meadows Row",
    "barbell one arm single deadlift", "Barbell pendlay row", "Barbell push bent over row", "Barbell Reverse Deadlift",
    "Barbell Reverse Deadlift_Female", "Barbell reverse grip bent over row", "Barbell romanian deadlift",
    "Barbell seated good morning", "Barbell single leg deadlift", "Barbell snatch deadlift close grip",
    "Barbell snatch deadlift wide grip", "barbell sumo deadlift", "Barbell sumo deadlift", "Barbell Supinated Pendlay Row",
    "Barbell Underhand Bent over Row", "Barbell Upright Row", "Barbell wide grip Upright Row", "Barbell Zercher Good Morning_Female",
    "behind neck lat pull down machine", "Bench Pull-ups", "bent over barbell row", "bent over row resistance band",
    "Bent-over Row with bar", "Bodyweight Muscle-up", "Cable Bar Lateral Pulldown", "Cable Bent Over Reverse Grip Row",
    "Cable Bent Over Row", "Cable Close Grip Front Lat Pulldown", "Cable Lat Prayer", "Cable Lat Prayer_Female",
    "cable low seated row", "cable lying extension pullover (with cables attachment)", "Cable One Arm Bent over Row",
    "Cable one arm lat pulldown", "cable palm rotational row", "Cable Pull In Kneeling", "Cable Pull In Kneeling_Female",
    "Cable Pulldown (pro lat bar)", "Cable Pulldown", "Cable Pushdown (straight arm)", "Cable rear Pulldown",
    "cable reverse grip straight back seated high row", "Cable Seated on Floor Row", "Cable Seated One Arm Alternate Row",
    "cable seated row", "Cable Seated Supine grip Row", "Cable Silverback Shrug", "Cable Silverback Shrug_Female",
    "Cable Straight Arm Pulldown", "Cable Twin Handle Parallel Grip Lat Pulldown", "Cable Twisting Pull",
    "Cable Underhand Pulldown Wide Grips", "chin up reverse close grip", "chin up reverse normal grip",
    "chin up reverse wide grip", "chin up", "Chin ups (narrow parallel grip)", "Chin-up (isometric and negative)",
    "Close-reverse Grip Chin-Up", "Commando pull-up", "Dumbbell Alternating Pendlay Row", "Dumbbell Alternating Pendlay Row_Female",
    "Dumbbell Bent Arm Pullover", "Dumbbell Bent-Over Reverse Row", "Dumbbell Bent-Over Row", "Dumbbell Goblet Good Morning",
    "Dumbbell Goblet Good Morning_Female", "Dumbbell Hammer Grip Incline Bench Two Arm Row", "Dumbbell Incline Row",
    "Dumbbell Knee Lawnmower Row", "Dumbbell Knee Lawnmower Row_Female", "Dumbbell One Arm Row (rack support)",
    "Dumbbell Pendlay Row", "Dumbbell Pendlay Row_Female", "Dumbbell Pullover (VERSION 2)", "Dumbbell Pullover on floor",
    "Dumbbell Pullover", "Dumbbell Renegade Row", "Dumbbell Romanian Deadlift", "dumbbells bent over row pronated grip",
    "dumbbells bent over row", "dumbbells plank to alternating row", "dumbbells single arm row", "Elbow Lift Reverse Push Up",
    "Exercise Ball Back Extension With Hands Behind Head", "good morning resistance band", "good mornings barbell",
    "good mornings dumbbells", "good mornings kettlebell", "Hammer Strength Plate Loaded High Row Alternate Arms",
    "Hammer Strength Plate Loaded High Row Alternate Arms_Female", "Hammer Strength Plate Loaded High Row Both Arms",
    "Hammer Strength Plate Loaded High Row Both Arms_Female", "Hammer Strength Plate Loaded High Row Single Arm",
    "Hammer Strength Plate Loaded High Row Single Arms_Female", "inverted rows on smith machine",
    "Jump_Rope row female white screen", "Jump_Rope row white screen", "Kettlebell Sumo Deadlift",
    "Kettlebells Sumo Deadlift with High Pull", "Landmine row", "lat pull down close grip", "lat pull down normal grip",
    "lat pull down wide grip", "lying back extension", "prone bench row barbell pronate grip",
    "Prone bench row barbell reverse grip", "prone bench row dumbbells pronated grip", "prone bench row dumbbells supinated grip",
    "prone bench row dumbbells", "pull up normal grip", "pull up wide grip", "pull up wide grip front view",
    "Push Pull Chair Over Head Pull", "Push Pull Chair Over Head Pull_Female", "resistance band - cable row",
    "Resistance Band Cross Body Single Straight Arm Supinated Pulldown", "Resistance Band Cross Body Single Straight Arm Supinated Pulldown_Female",
    "Resistance Band Floor Hyperextension", "Resistance Band Floor Hyperextension_Female",
    "Resistance Band Kneeling Cross Body Single Straight Arm Supinated Pulldown",
    "Resistance Band Kneeling Cross Body Single Straight Arm Supinated Pulldown_Female",
    "Resistance Band Lying Hyperextension Abduction", "Resistance Band Lying Hyperextension Abduction_Female",
    "reverse grip lat pull down", "seated cable row V bar machine", "seated normal grip row machine",
    "seated resistance band - cable rows", "single arm cable row left", "single arm cable row right",
    "single arm standing low cable row", "Single Kettlebell Deadlift", "standing lat cable pull over",
    "straight bar cable pull down", "straight bar cable row close grip", "straight bar cable row normal grip",
    "superman", "superman pulls", "superman pulls resistance band", "Supine Push up",
    "Suspension Trainer with Grips Bent Knee Inverted Row", "Suspension Trainer with Grips Bent Knee Inverted Row_female",
    "Suspension Trainer with Grips Chin-up", "Suspension Trainer with Grips Chin-up_female",
    "Suspension Trainer with Grips High Row", "Suspension Trainer with Grips High Row_female",
    "Suspension Trainer with Grips Inverted Row Arm Twist", "Suspension Trainer with Grips Inverted Row Arm Twist_female",
    "Suspension Trainer with Grips Inverted Row female", "Suspension Trainer with Grips Inverted Row",
    "Suspension Trainer with Grips Knee Tuck Row", "Suspension Trainer with Grips Knee Tuck Row_female",
    "Suspension Trainer with Grips Pull-up", "Suspension Trainer with Grips Pull-up_female",
    "Suspension Trainer with Grips Wide Grip Inverted Row on floor", "Suspension Trainer with Grips Wide Grip Inverted Row on floor_female",
    "trap bar deadlift", "v bar lat pull down"
]

def add_exercises_to_database():
    """Add all exercises to the database"""
    session = SessionLocal()
    try:
        logger.info("🌱 Starting to add comprehensive exercise list...")
        
        # Process Abdominal exercises
        logger.info(f"📊 Adding {len(ABDOMINAL_EXERCISES)} abdominal exercises...")
        for i, exercise_name in enumerate(ABDOMINAL_EXERCISES, 1):
            category, muscle_groups, base_difficulty = categorize_exercise(exercise_name, "ABDOMINALS")
            equipment = determine_equipment(exercise_name)
            difficulty = determine_difficulty(exercise_name, base_difficulty)
            slug = create_slug(exercise_name, session)
            
            exercise = Exercise(
                name=exercise_name,
                slug=slug,
                description=f"Abdominal exercise targeting {', '.join(muscle_groups).lower()}",
                instructions=[
                    "Set up in the starting position",
                    "Perform the movement with controlled form",
                    "Focus on engaging the target muscles",
                    "Return to starting position"
                ],
                tips=[
                    "Maintain proper form throughout",
                    "Control the movement speed",
                    "Breathe properly during execution"
                ],
                muscle_groups=muscle_groups,
                equipment=equipment,
                difficulty=difficulty,
                exercise_type="strength",
                category=category,
                duration_minutes=3,
                calories_per_minute=5.0,
                is_active=True
            )
            
            session.add(exercise)
            session.commit()  # Commit each exercise individually
            logger.info(f"✅ Added abdominal exercise {i}: {exercise_name}")

            if i % 50 == 0:
                logger.info(f"📊 Progress: {i}/{len(ABDOMINAL_EXERCISES)} abdominal exercises added")
        
        session.commit()
        logger.info(f"✅ Successfully added all {len(ABDOMINAL_EXERCISES)} abdominal exercises")
        
        # Process Back exercises
        logger.info(f"📊 Adding {len(BACK_EXERCISES)} back exercises...")
        for i, exercise_name in enumerate(BACK_EXERCISES, 1):
            category, muscle_groups, base_difficulty = categorize_exercise(exercise_name, "BACK")
            equipment = determine_equipment(exercise_name)
            difficulty = determine_difficulty(exercise_name, base_difficulty)
            slug = create_slug(exercise_name, session)
            
            exercise = Exercise(
                name=exercise_name,
                slug=slug,
                description=f"Back exercise targeting {', '.join(muscle_groups).lower()}",
                instructions=[
                    "Set up in the starting position",
                    "Perform the movement with controlled form",
                    "Focus on engaging the target muscles",
                    "Return to starting position"
                ],
                tips=[
                    "Maintain proper form throughout",
                    "Control the movement speed",
                    "Breathe properly during execution"
                ],
                muscle_groups=muscle_groups,
                equipment=equipment,
                difficulty=difficulty,
                exercise_type="strength",
                category=category,
                duration_minutes=4,
                calories_per_minute=6.0,
                is_active=True
            )
            
            session.add(exercise)
            session.commit()  # Commit each exercise individually
            logger.info(f"✅ Added back exercise {i}: {exercise_name}")

            if i % 50 == 0:
                logger.info(f"📊 Progress: {i}/{len(BACK_EXERCISES)} back exercises added")
        
        session.commit()
        logger.info(f"✅ Successfully added all {len(BACK_EXERCISES)} back exercises")
        
        # Get final count
        total_exercises = session.query(Exercise).count()
        logger.info(f"🎉 Database now contains {total_exercises} total exercises!")
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error adding exercises: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    add_exercises_to_database()
