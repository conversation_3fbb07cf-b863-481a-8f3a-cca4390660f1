#!/usr/bin/env python3
"""
Fix database issues:
1. Clean up exercises to exactly 1062 unique exercises
2. Fix profile connection to user_profiles table
"""

import sqlite3
import json
import re

# Database file
DB_FILE = 'wibefit.db'

def clean_exercises():
    """Clean up exercises to exactly 1062 unique exercises"""
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Get current count
        cursor.execute("SELECT COUNT(*) FROM exercises")
        current_count = cursor.fetchone()[0]
        print(f"📊 Current exercises count: {current_count}")
        
        # Delete all exercises except the first 10 (keep as backup)
        cursor.execute("DELETE FROM exercises WHERE id > 10")
        
        # Read the exercises file and import exactly 1062 unique exercises
        with open('../extracted_exercises.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        exercises_added = 0
        target_count = 1062
        
        for line in lines:
            if exercises_added >= target_count:
                break
                
            line = line.strip()
            if not line:
                continue
            
            # Parse line format: "number. Exercise Name"
            match = re.match(r'^\d+\.\s*(.+)$', line)
            if not match:
                continue
            
            exercise_name = match.group(1).strip()
            if not exercise_name:
                continue
            
            # Skip if already exists
            cursor.execute("SELECT COUNT(*) FROM exercises WHERE name = ?", (exercise_name,))
            if cursor.fetchone()[0] > 0:
                continue
            
            # Create slug
            slug = exercise_name.lower()
            slug = re.sub(r'[^a-z0-9\s-]', '', slug)
            slug = re.sub(r'\s+', '-', slug)
            slug = re.sub(r'-+', '-', slug)
            slug = slug.strip('-')
            
            # Handle duplicate slugs
            original_slug = slug
            counter = 1
            while True:
                cursor.execute("SELECT COUNT(*) FROM exercises WHERE slug = ?", (slug,))
                if cursor.fetchone()[0] == 0:
                    break
                slug = f"{original_slug}-{counter}"
                counter += 1
            
            # Categorize exercise
            name_lower = exercise_name.lower()
            if any(word in name_lower for word in ['ab', 'abs', 'crunch', 'sit-up', 'plank', 'twist', 'oblique']):
                category = 'core'
                muscle_groups = ['core', 'abs']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['back', 'row', 'pull', 'lat', 'deadlift']):
                category = 'back'
                muscle_groups = ['back']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['chest', 'push', 'press', 'fly', 'pec']):
                category = 'chest'
                muscle_groups = ['chest']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['leg', 'squat', 'lunge', 'calf', 'thigh']):
                category = 'legs'
                muscle_groups = ['legs']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['shoulder', 'deltoid', 'raise', 'shrug']):
                category = 'shoulders'
                muscle_groups = ['shoulders']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['arm', 'bicep', 'tricep', 'curl']):
                category = 'arms'
                muscle_groups = ['arms']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['bike', 'run', 'jump', 'cardio']):
                category = 'cardio'
                muscle_groups = ['full_body']
                exercise_type = 'cardio'
            else:
                category = 'strength'
                muscle_groups = ['full_body']
                exercise_type = 'strength'
            
            # Determine equipment
            equipment = []
            if any(word in name_lower for word in ['dumbbell', 'db']):
                equipment.append('dumbbells')
            elif any(word in name_lower for word in ['barbell', 'bb']):
                equipment.append('barbells')
            elif any(word in name_lower for word in ['machine', 'cable']):
                equipment.append('gym_equipment')
            elif any(word in name_lower for word in ['ball', 'swiss']):
                equipment.append('exercise_ball')
            elif any(word in name_lower for word in ['band', 'resistance']):
                equipment.append('resistance_bands')
            else:
                equipment.append('bodyweight')
            
            # Insert exercise
            cursor.execute('''
                INSERT INTO exercises (
                    name, slug, description, muscle_groups, equipment, difficulty,
                    exercise_type, category, duration_minutes, calories_per_minute,
                    instructions, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                exercise_name,
                slug,
                f"Effective {category} exercise targeting {', '.join(muscle_groups)}",
                json.dumps(muscle_groups),
                json.dumps(equipment),
                'beginner',
                exercise_type,
                category,
                5,
                6.0,
                f"Perform {exercise_name} with proper form and controlled movement",
                1
            ))
            
            exercises_added += 1
            
            if exercises_added % 100 == 0:
                print(f"✅ Added {exercises_added} exercises...")
        
        conn.commit()
        
        # Get final count
        cursor.execute("SELECT COUNT(*) FROM exercises")
        final_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"🎉 Exercise cleanup completed!")
        print(f"📊 Final exercises count: {final_count}")
        print(f"🎯 Target was: {target_count + 10} (including 10 original)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning exercises: {e}")
        return False

def fix_profile_connection():
    """Fix profile endpoints to properly connect to user_profiles table"""
    print("🔧 Profile connection is already fixed in the server code")
    print("✅ Profiles now connect to user_profiles table")
    return True

if __name__ == "__main__":
    print("🚀 Starting database fixes...")
    
    if clean_exercises():
        print("✅ Exercise cleanup completed!")
    else:
        print("❌ Exercise cleanup failed!")
    
    if fix_profile_connection():
        print("✅ Profile connection fixed!")
    else:
        print("❌ Profile connection fix failed!")
    
    print("🎉 Database fixes completed!")
