 #!/usr/bin/env python3
"""
Test script to verify user registration is working with the database
"""

import requests
import json

# Test registration endpoint
def test_registration():
    url = "http://localhost:8000/api/register"
    
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "username": "testuser",
        "role": "user",
        "bio": "Test user for database verification",
        "phone_number": "+1234567890",
        "timezone": "UTC",
        "language": "en"
    }
    
    try:
        print("🔄 Testing user registration...")
        response = requests.post(url, json=test_user, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Registration successful!")
            user_data = response.json()
            print(f"User ID: {user_data.get('id')}")
            print(f"Email: {user_data.get('email')}")
            print(f"Full Name: {user_data.get('full_name')}")
            return True
        else:
            print("❌ Registration failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during registration: {e}")
        return False

# Test login endpoint
def test_login():
    url = "http://localhost:8000/api/login"
    
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        print("\n🔄 Testing user login...")
        response = requests.post(url, json=login_data, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            token_data = response.json()
            print(f"Access Token: {token_data.get('access_token')[:50]}...")
            return True
        else:
            print("❌ Login failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing WibeFit Backend Registration & Database Integration")
    print("=" * 60)
    
    # Test registration
    registration_success = test_registration()
    
    # Test login if registration was successful
    if registration_success:
        login_success = test_login()
        
        if login_success:
            print("\n🎉 All tests passed! Database integration is working correctly.")
        else:
            print("\n⚠️ Registration worked but login failed.")
    else:
        print("\n❌ Registration failed. Check backend logs for details.")
    
    print("\n📊 Test Summary:")
    print(f"Registration: {'✅ PASS' if registration_success else '❌ FAIL'}")
    if registration_success:
        print(f"Login: {'✅ PASS' if login_success else '❌ FAIL'}")
