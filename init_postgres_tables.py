#!/usr/bin/env python3
"""
Initialize PostgreSQL database tables for WibeFit
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from app.core.config import settings
from app.core.database import Base
import logging

# Import all models to ensure they are registered with Base
from app.models.user import User
from app.models.exercise import Exercise, MuscleGroup, Equipment
from app.models.workout import WorkoutPlan
from app.models.community import CommunityPost

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_tables():
    """Create all database tables"""
    try:
        logger.info("🚀 Creating PostgreSQL database tables...")
        
        # Create engine
        engine = create_engine(settings.database_url)
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Database tables created successfully!")
        logger.info("📊 Tables created:")
        
        # List all tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        table_names = inspector.get_table_names()
        
        for table_name in sorted(table_names):
            logger.info(f"   - {table_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")
        return False

def main():
    """Main initialization function"""
    logger.info("🐘 Initializing PostgreSQL database schema...")
    
    if create_tables():
        logger.info("🎉 PostgreSQL database initialization completed!")
        logger.info("Next steps:")
        logger.info("1. Run: python3 seed_postgres.py")
        logger.info("2. Test API: http://localhost:8000/docs")
        return True
    else:
        logger.error("❌ PostgreSQL database initialization failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
