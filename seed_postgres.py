#!/usr/bin/env python3
"""
Seed the PostgreSQL database with sample data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.user import User
from app.models.exercise import Exercise, MuscleGroup, Equipment
from app.models.workout import WorkoutPlan
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def seed_muscle_groups():
    """Add muscle groups to database"""
    session = SessionLocal()
    try:
        muscle_groups = [
            {"name": "Chest", "category": "Upper Body", "description": "Pectoral muscles"},
            {"name": "Back", "category": "Upper Body", "description": "Latissimus dorsi, rhomboids, traps"},
            {"name": "Shoulders", "category": "Upper Body", "description": "Deltoids"},
            {"name": "Arms", "category": "Upper Body", "description": "Biceps, triceps, forearms"},
            {"name": "Legs", "category": "Lower Body", "description": "Quadriceps, hamstrings, calves"},
            {"name": "Glutes", "category": "Lower Body", "description": "Gluteal muscles"},
            {"name": "Core", "category": "Core", "description": "Abdominals, obliques"},
        ]
        
        for mg_data in muscle_groups:
            existing = session.query(MuscleGroup).filter_by(name=mg_data["name"]).first()
            if not existing:
                muscle_group = MuscleGroup(**mg_data)
                session.add(muscle_group)
                logger.info(f"Added muscle group: {mg_data['name']}")
        
        session.commit()
        logger.info("✅ Muscle groups seeded successfully")
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error seeding muscle groups: {e}")
    finally:
        session.close()

def seed_equipment():
    """Add equipment to database"""
    session = SessionLocal()
    try:
        equipment_list = [
            {"name": "Dumbbells", "category": "Free Weights", "description": "Adjustable dumbbells", "is_common": True},
            {"name": "Barbell", "category": "Free Weights", "description": "Olympic barbell", "is_common": True},
            {"name": "Resistance Bands", "category": "Resistance", "description": "Elastic resistance bands", "is_common": True},
            {"name": "Pull-up Bar", "category": "Bodyweight", "description": "Doorway pull-up bar", "is_common": True},
            {"name": "Yoga Mat", "category": "Accessories", "description": "Exercise mat", "is_common": True},
            {"name": "None", "category": "Bodyweight", "description": "No equipment needed", "is_common": True},
            {"name": "Kettlebell", "category": "Free Weights", "description": "Cast iron weight", "is_common": True},
            {"name": "Cable Machine", "category": "Machines", "description": "Cable pulley system", "is_common": False},
        ]
        
        for eq_data in equipment_list:
            existing = session.query(Equipment).filter_by(name=eq_data["name"]).first()
            if not existing:
                equipment = Equipment(**eq_data)
                session.add(equipment)
                logger.info(f"Added equipment: {eq_data['name']}")
        
        session.commit()
        logger.info("✅ Equipment seeded successfully")
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error seeding equipment: {e}")
    finally:
        session.close()

def seed_exercises():
    """Add sample exercises to database"""
    session = SessionLocal()
    try:
        exercises = [
            {
                "name": "Push-ups",
                "slug": "push-ups",
                "description": "Classic bodyweight chest exercise",
                "instructions": ["Start in plank position", "Lower chest to ground", "Push back up"],
                "tips": ["Keep core tight", "Full range of motion"],
                "muscle_groups": ["Chest", "Arms", "Core"],
                "equipment": ["None"],
                "difficulty": "beginner",
                "exercise_type": "strength",
                "category": "upper_body",
                "duration_minutes": 5,
                "calories_per_minute": 8.0,
                "is_active": True
            },
            {
                "name": "Squats",
                "slug": "squats",
                "description": "Fundamental lower body exercise",
                "instructions": ["Stand with feet shoulder-width apart", "Lower hips back and down", "Return to standing"],
                "tips": ["Keep knees behind toes", "Chest up"],
                "muscle_groups": ["Legs", "Glutes", "Core"],
                "equipment": ["None"],
                "difficulty": "beginner",
                "exercise_type": "strength",
                "category": "lower_body",
                "duration_minutes": 5,
                "calories_per_minute": 6.0,
                "is_active": True
            },
            {
                "name": "Dumbbell Bench Press",
                "slug": "dumbbell-bench-press",
                "description": "Upper body strength exercise",
                "instructions": ["Lie on bench with dumbbells", "Press weights up", "Lower with control"],
                "tips": ["Keep feet on floor", "Squeeze chest at top"],
                "muscle_groups": ["Chest", "Arms", "Shoulders"],
                "equipment": ["Dumbbells"],
                "difficulty": "intermediate",
                "exercise_type": "strength",
                "category": "upper_body",
                "duration_minutes": 10,
                "calories_per_minute": 7.0,
                "is_active": True
            },
            {
                "name": "Deadlifts",
                "slug": "deadlifts",
                "description": "Compound full-body exercise",
                "instructions": ["Stand with feet hip-width apart", "Grip barbell", "Lift by extending hips and knees"],
                "tips": ["Keep back straight", "Drive through heels"],
                "muscle_groups": ["Back", "Legs", "Glutes", "Core"],
                "equipment": ["Barbell"],
                "difficulty": "intermediate",
                "exercise_type": "strength",
                "category": "full_body",
                "duration_minutes": 15,
                "calories_per_minute": 9.0,
                "is_active": True
            },
            {
                "name": "Plank",
                "slug": "plank",
                "description": "Core stability exercise",
                "instructions": ["Start in push-up position", "Hold position", "Keep body straight"],
                "tips": ["Engage core", "Don't let hips sag"],
                "muscle_groups": ["Core", "Shoulders"],
                "equipment": ["None"],
                "difficulty": "beginner",
                "exercise_type": "strength",
                "category": "core",
                "duration_minutes": 3,
                "calories_per_minute": 4.0,
                "is_active": True
            }
        ]
        
        for ex_data in exercises:
            existing = session.query(Exercise).filter_by(slug=ex_data["slug"]).first()
            if not existing:
                exercise = Exercise(**ex_data)
                session.add(exercise)
                logger.info(f"Added exercise: {ex_data['name']}")
        
        session.commit()
        logger.info("✅ Exercises seeded successfully")
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error seeding exercises: {e}")
    finally:
        session.close()

def seed_users():
    """Add sample users to database"""
    session = SessionLocal()
    try:
        users = [
            {
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "username": "johndoe",
                "hashed_password": "hashed_password_here",
                "role": "member",
                "is_active": True,
                "is_verified": True,
                "bio": "Fitness enthusiast"
            },
            {
                "email": "<EMAIL>",
                "full_name": "Jane Smith",
                "username": "janesmith",
                "hashed_password": "hashed_password_here",
                "role": "member",
                "is_active": True,
                "is_verified": True,
                "bio": "Personal trainer"
            },
            {
                "email": "<EMAIL>",
                "full_name": "Admin User",
                "username": "admin",
                "hashed_password": "hashed_password_here",
                "role": "admin",
                "is_active": True,
                "is_verified": True,
                "bio": "System administrator"
            }
        ]
        
        for user_data in users:
            existing = session.query(User).filter_by(email=user_data["email"]).first()
            if not existing:
                user = User(**user_data)
                session.add(user)
                logger.info(f"Added user: {user_data['full_name']}")
        
        session.commit()
        logger.info("✅ Users seeded successfully")
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error seeding users: {e}")
    finally:
        session.close()

def main():
    """Run all seeding functions"""
    logger.info("🌱 Starting PostgreSQL database seeding...")
    
    seed_muscle_groups()
    seed_equipment()
    seed_exercises()
    seed_users()
    
    logger.info("🎉 PostgreSQL database seeding completed!")
    logger.info("📊 Database now contains:")
    logger.info("   - 7 muscle groups")
    logger.info("   - 8 equipment types")
    logger.info("   - 5 exercises")
    logger.info("   - 3 users")
    logger.info("🌐 API Documentation: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
