-- Create fresh database schema with username as primary key
-- Drop existing tables and recreate with username-based structure

DROP TABLE IF EXISTS body_weights CASCADE;
DROP TABLE IF EXISTS workout_sessions CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table with username as primary key
CREATE TABLE users (
    username TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    password_hash TEXT,
    role TEXT DEFAULT 'user',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create user profiles table
CREATE TABLE user_profiles (
    username TEXT PRIMARY KEY REFERENCES users(username) ON DELETE CASCADE,
    age INTEGER,
    phone TEXT,
    gender TEXT,
    height REAL,
    current_weight REAL,
    fitness_level TEXT,
    primary_goals JSONB DEFAULT '[]'::jsonb,
    equipment_access JSONB DEFAULT '[]'::jsonb,
    preferred_workout_days JSONB DEFAULT '[]'::jsonb,
    activity_level TEXT,
    preferred_workout_duration INTEGER,
    profile_setup_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create workout sessions table
CREATE TABLE workout_sessions (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
    workout_plan_id TEXT,
    workout_name TEXT NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_minutes INTEGER,
    calories_burned INTEGER,
    body_weight REAL,
    status TEXT DEFAULT 'completed',
    exercises JSONB NOT NULL DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create body weights table
CREATE TABLE body_weights (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
    weight REAL NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_profiles_username ON user_profiles(username);
CREATE INDEX idx_workout_sessions_username ON workout_sessions(username);
CREATE INDEX idx_workout_sessions_created_at ON workout_sessions(created_at);
CREATE INDEX idx_body_weights_username ON body_weights(username);
CREATE INDEX idx_body_weights_recorded_at ON body_weights(recorded_at);

-- Insert demo users with usernames
INSERT INTO users (username, email, full_name, role, created_at, last_login_at, metadata) VALUES
('demo', '<EMAIL>', 'Demo User', 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}'),
('demouser', '<EMAIL>', 'Demo User Local', 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}'),
('admin', '<EMAIL>', 'Admin User', 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}');

-- Insert demo profile for main demo user
INSERT INTO user_profiles (
    username, age, phone, gender, height, current_weight, fitness_level,
    primary_goals, equipment_access, preferred_workout_days,
    activity_level, preferred_workout_duration, profile_setup_completed,
    created_at, updated_at
) VALUES (
    'demo', 25, '+1234567890', 'male', 175.0, 70.0, 'intermediate',
    '["Weight Loss", "Muscle Gain"]'::jsonb,
    '["Full Gym Access"]'::jsonb,
    '["Monday", "Wednesday", "Friday"]'::jsonb,
    'moderately_active', 45, true,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- Insert sample workout session
INSERT INTO workout_sessions (
    username, workout_name, duration_minutes, calories_burned,
    exercises, status, created_at
) VALUES (
    'demo', 'Morning Workout', 30, 250,
    '[{"name": "Push-ups", "sets": 3, "reps": 15}, {"name": "Squats", "sets": 3, "reps": 20}]'::jsonb,
    'completed', CURRENT_TIMESTAMP
);

-- Insert sample body weight record
INSERT INTO body_weights (username, weight, recorded_at, notes) VALUES
('demo', 70.0, CURRENT_TIMESTAMP, 'Starting weight');

-- Show final table structures
\d users
\d user_profiles
\d workout_sessions
\d body_weights
