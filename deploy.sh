#!/bin/bash

echo "🚀 WibeFit Server Deployment Script"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

echo "📦 Building Docker image..."
docker build -t wibefit-server .

echo "🔄 Stopping existing container..."
docker stop wibefit-server 2>/dev/null || true
docker rm wibefit-server 2>/dev/null || true

echo "🚀 Starting new container..."
docker run -d \
    --name wibefit-server \
    -p 8000:8000 \
    --restart unless-stopped \
    wibefit-server

echo "✅ WibeFit server deployed successfully!"
echo "🌐 Server available at: http://localhost:8000"
echo "📊 Check status: docker ps | grep wibefit-server"
echo "📝 View logs: docker logs wibefit-server"
echo "🛑 Stop server: docker stop wibefit-server"
