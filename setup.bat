@echo off
REM ============================================================================
REM WibeFit App - Quick Setup Script for Windows
REM ============================================================================
REM This script helps set up the WibeFit development environment on Windows
REM Run by double-clicking this file or running: setup.bat

setlocal enabledelayedexpansion

echo 🏋️ WibeFit App - Quick Setup Script (Windows)
echo =============================================
echo.

REM Check if we're in the right directory
if not exist "requirements.txt" (
    echo [ERROR] requirements.txt not found
    echo Please run this script from the WibeFit project root directory
    pause
    exit /b 1
)

if not exist "frontend" (
    echo [ERROR] frontend directory not found
    echo Please run this script from the WibeFit project root directory
    pause
    exit /b 1
)

if not exist "backend" (
    echo [ERROR] backend directory not found
    echo Please run this script from the WibeFit project root directory
    pause
    exit /b 1
)

echo [INFO] Checking system requirements...

REM Check Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Flutter not found. Please install Flutter first.
    echo Download from: https://docs.flutter.dev/get-started/install
    pause
    exit /b 1
) else (
    echo [SUCCESS] Flutter found
)

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found. Please install Python 3.8+ first.
    echo Download from: https://python.org/downloads/
    pause
    exit /b 1
) else (
    echo [SUCCESS] Python found
)

echo.
echo [INFO] Setting up Flutter dependencies...

cd frontend

echo [INFO] Running Flutter doctor...
flutter doctor

echo [INFO] Installing Flutter dependencies...
flutter pub get

if %errorlevel% neq 0 (
    echo [ERROR] Flutter setup failed
    pause
    exit /b 1
)

echo [SUCCESS] Flutter setup complete!
cd ..

echo.
echo [INFO] Setting up Python backend...

cd backend

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
) else (
    echo [INFO] Virtual environment already exists
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo [INFO] Installing Python dependencies...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo [ERROR] Backend setup failed
    pause
    exit /b 1
)

echo [SUCCESS] Backend setup complete!
cd ..

echo.
echo [INFO] Checking available devices...
cd frontend
flutter devices
cd ..

echo.
echo 🎉 Setup Complete!
echo ==================
echo.
echo Next steps:
echo 1. Start the backend server:
echo    cd backend
echo    venv\Scripts\activate.bat
echo    python wibefit_postgresql_server.py
echo    # OR
echo    python wibefit_v1_server.py
echo.
echo 2. In a new command prompt, start the Flutter app:
echo    cd frontend
echo    flutter run
echo.
echo 3. Choose your target device when prompted
echo.
echo 📖 For detailed instructions, see README.md
echo 🆘 For issues, check requirements.txt troubleshooting section
echo.
echo [SUCCESS] Happy coding! 🚀
echo.
pause
