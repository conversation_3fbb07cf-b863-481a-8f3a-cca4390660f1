# WibeFit Docker Compose Configuration
# This file sets up the complete WibeFit environment with Docker

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:13
    container_name: wibefit_postgres
    environment:
      POSTGRES_DB: wibefit_db
      POSTGRES_USER: sanju
      POSTGRES_PASSWORD: wibefit123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - wibefit_network
    restart: unless-stopped

  # Python Backend
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wibefit_backend
    environment:
      DATABASE_URL: *******************************************/wibefit_db
      SECRET_KEY: wibefit_secret_key_change_in_production
      DEBUG: "True"
      ENVIRONMENT: development
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - .:/app
    networks:
      - wibefit_network
    restart: unless-stopped
    command: uvicorn main:app --host 0.0.0.0 --port 8000

  # Redis (for future caching/sessions)
  redis:
    image: redis:7-alpine
    container_name: wibefit_redis
    ports:
      - "6379:6379"
    networks:
      - wibefit_network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  wibefit_network:
    driver: bridge

# Development override file
# Create docker-compose.override.yml for local development settings
