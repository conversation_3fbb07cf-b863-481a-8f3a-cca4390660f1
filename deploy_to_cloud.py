#!/usr/bin/env python3
"""
Deploy WibeFit server to cloud for cross-device support
"""

import os
import shutil

def create_dockerfile():
    """Create Dockerfile for cloud deployment"""
    dockerfile_content = '''FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    sqlite3 \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY . .

# Create database directory
RUN mkdir -p /app/data

# Copy database
COPY wibefit.db /app/data/wibefit.db

# Expose port
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app
ENV DATABASE_PATH=/app/data/wibefit.db

# Run the application
CMD ["python", "wibefit_sqlite_server.py"]
'''
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    print("✅ Dockerfile created")

def create_requirements_txt():
    """Create requirements.txt for deployment"""
    requirements = '''flask==2.3.3
flask-cors==4.0.0
werkzeug==2.3.7
'''
    
    with open('requirements.txt', 'w') as f:
        f.write(requirements)
    print("✅ requirements.txt created")

def create_railway_config():
    """Create Railway deployment configuration"""
    railway_config = '''{
  "build": {
    "builder": "DOCKERFILE"
  },
  "deploy": {
    "startCommand": "python wibefit_sqlite_server.py",
    "healthcheckPath": "/health"
  }
}'''
    
    with open('railway.json', 'w') as f:
        f.write(railway_config)
    print("✅ Railway configuration created")

def create_heroku_config():
    """Create Heroku deployment files"""
    # Procfile
    with open('Procfile', 'w') as f:
        f.write('web: python wibefit_sqlite_server.py\n')
    
    # runtime.txt
    with open('runtime.txt', 'w') as f:
        f.write('python-3.9.19\n')
    
    print("✅ Heroku configuration created")

def create_deployment_script():
    """Create deployment script"""
    deploy_script = '''#!/bin/bash

echo "🚀 WibeFit Server Deployment Script"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

echo "📦 Building Docker image..."
docker build -t wibefit-server .

echo "🔄 Stopping existing container..."
docker stop wibefit-server 2>/dev/null || true
docker rm wibefit-server 2>/dev/null || true

echo "🚀 Starting new container..."
docker run -d \\
    --name wibefit-server \\
    -p 8000:8000 \\
    --restart unless-stopped \\
    wibefit-server

echo "✅ WibeFit server deployed successfully!"
echo "🌐 Server available at: http://localhost:8000"
echo "📊 Check status: docker ps | grep wibefit-server"
echo "📝 View logs: docker logs wibefit-server"
echo "🛑 Stop server: docker stop wibefit-server"
'''
    
    with open('deploy.sh', 'w') as f:
        f.write(deploy_script)
    
    # Make executable
    os.chmod('deploy.sh', 0o755)
    print("✅ Deployment script created")

def create_cloud_instructions():
    """Create cloud deployment instructions"""
    instructions = '''# WibeFit Server Cloud Deployment Guide

## Option 1: Railway (Recommended - Free Tier Available)

1. **Sign up at Railway.app**
   - Go to https://railway.app
   - Sign up with GitHub

2. **Deploy from GitHub**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Add cloud deployment"
   git push origin main
   ```

3. **Create Railway Project**
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your repository
   - Railway will auto-detect the Dockerfile

4. **Set Environment Variables**
   - Go to Variables tab
   - Add: `PORT=8000`
   - Add: `DATABASE_PATH=/app/data/wibefit.db`

5. **Get Your URL**
   - Railway will provide a URL like: `https://your-app.railway.app`
   - Update your Flutter app to use this URL

## Option 2: Heroku (Free Tier Discontinued)

1. **Install Heroku CLI**
   ```bash
   # macOS
   brew tap heroku/brew && brew install heroku
   
   # Login
   heroku login
   ```

2. **Create Heroku App**
   ```bash
   heroku create your-wibefit-app
   git push heroku main
   ```

## Option 3: DigitalOcean App Platform

1. **Sign up at DigitalOcean**
2. **Create App from GitHub**
3. **Configure Build Settings**
   - Build Command: `docker build .`
   - Run Command: `python wibefit_sqlite_server.py`

## Option 4: Local Docker (For Development)

```bash
# Build and run locally
./deploy.sh

# Your server will be available at:
# http://localhost:8000
```

## Update Flutter App

After deployment, update your Flutter app:

```dart
// In hybrid_database_service.dart
static const String _onlineBaseUrl = 'https://your-app.railway.app';
```

## Testing Cross-Device Login

1. **Deploy server to cloud**
2. **Update Flutter app with cloud URL**
3. **Build new APK**
4. **Install on multiple devices**
5. **Register on Device A**
6. **Login on Device B** ✅

## Benefits of Cloud Deployment

✅ **Cross-Device Login**: Register once, login anywhere
✅ **Data Sync**: Shared data across all devices
✅ **Always Available**: 24/7 server uptime
✅ **Automatic Backups**: Cloud provider handles backups
✅ **Scalability**: Handles multiple users
'''
    
    with open('CLOUD_DEPLOYMENT.md', 'w') as f:
        f.write(instructions)
    print("✅ Cloud deployment instructions created")

if __name__ == "__main__":
    print("🚀 Creating cloud deployment files...")
    
    create_dockerfile()
    create_requirements_txt()
    create_railway_config()
    create_heroku_config()
    create_deployment_script()
    create_cloud_instructions()
    
    print("\n🎉 Cloud deployment files created successfully!")
    print("\n📋 Next steps:")
    print("1. Choose a cloud provider (Railway recommended)")
    print("2. Follow instructions in CLOUD_DEPLOYMENT.md")
    print("3. Update Flutter app with cloud URL")
    print("4. Build new APK with cross-device support")
    print("\n✅ After deployment, users can login from any device!")
