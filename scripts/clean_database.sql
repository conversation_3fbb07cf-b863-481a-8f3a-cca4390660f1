-- WibeFit Database Cleanup Script
-- This script removes all fake/test data and resets the database to a clean state

-- Disable foreign key checks temporarily (PostgreSQL equivalent)
SET session_replication_role = replica;

-- Delete all data from tables (in correct order to avoid foreign key conflicts)
DELETE FROM post_shares;
DELETE FROM post_comments;
DELETE FROM post_likes;
DELETE FROM community_posts;
DELETE FROM group_posts;
DELETE FROM group_members;
DELETE FROM groups;
DELETE FROM user_follows;
DELETE FROM notifications;
DELETE FROM reports;
DELETE FROM challenge_participants;
DELETE FROM challenges;
DELETE FROM user_achievements;
DELETE FROM user_workouts;
DELETE FROM workout_sessions;
DELETE FROM workout_exercises;
DELETE FROM workout_plans;
DELETE FROM exercise_logs;
DELETE FROM user_exercises;
DELETE FROM exercise_variations;
DELETE FROM exercises;
DELETE FROM equipment;
DELETE FROM muscle_groups;
DELETE FROM user_goals;
DELETE FROM user_measurements;
DELETE FROM user_profiles;
DELETE FROM friendships;
DELETE FROM users;

-- Reset all sequences to start from 1
ALTER SEQUENCE users_id_seq RESTART WITH 1;
ALTER SEQUENCE user_profiles_id_seq RESTART WITH 1;
ALTER SEQUENCE user_measurements_id_seq RESTART WITH 1;
ALTER SEQUENCE user_goals_id_seq RESTART WITH 1;
ALTER SEQUENCE user_achievements_id_seq RESTART WITH 1;
ALTER SEQUENCE friendships_id_seq RESTART WITH 1;
ALTER SEQUENCE exercises_id_seq RESTART WITH 1;
ALTER SEQUENCE exercise_variations_id_seq RESTART WITH 1;
ALTER SEQUENCE user_exercises_id_seq RESTART WITH 1;
ALTER SEQUENCE exercise_logs_id_seq RESTART WITH 1;
ALTER SEQUENCE muscle_groups_id_seq RESTART WITH 1;
ALTER SEQUENCE equipment_id_seq RESTART WITH 1;
ALTER SEQUENCE workout_plans_id_seq RESTART WITH 1;
ALTER SEQUENCE workout_exercises_id_seq RESTART WITH 1;
ALTER SEQUENCE workout_sessions_id_seq RESTART WITH 1;
ALTER SEQUENCE user_workouts_id_seq RESTART WITH 1;
ALTER SEQUENCE challenges_id_seq RESTART WITH 1;
ALTER SEQUENCE challenge_participants_id_seq RESTART WITH 1;
ALTER SEQUENCE community_posts_id_seq RESTART WITH 1;
ALTER SEQUENCE post_likes_id_seq RESTART WITH 1;
ALTER SEQUENCE post_comments_id_seq RESTART WITH 1;
ALTER SEQUENCE post_shares_id_seq RESTART WITH 1;
ALTER SEQUENCE user_follows_id_seq RESTART WITH 1;
ALTER SEQUENCE groups_id_seq RESTART WITH 1;
ALTER SEQUENCE group_members_id_seq RESTART WITH 1;
ALTER SEQUENCE group_posts_id_seq RESTART WITH 1;
ALTER SEQUENCE notifications_id_seq RESTART WITH 1;
ALTER SEQUENCE reports_id_seq RESTART WITH 1;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Verify cleanup
SELECT 'Cleanup completed successfully!' as status;
SELECT 
  'users' as table_name, COUNT(*) as remaining_records FROM users
UNION ALL
SELECT 'exercises', COUNT(*) FROM exercises
UNION ALL
SELECT 'muscle_groups', COUNT(*) FROM muscle_groups
UNION ALL
SELECT 'equipment', COUNT(*) FROM equipment
ORDER BY table_name;
