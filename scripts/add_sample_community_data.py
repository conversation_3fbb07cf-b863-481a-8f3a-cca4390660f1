#!/usr/bin/env python3
"""
Add sample community data to the database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta
import logging

from app.core.config import settings
from app.models.community import CommunityPost, PostLike, PostComment
from app.models.workout import Challenge, ChallengeParticipant
from app.models.user import User, Friendship

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create database connection
engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def add_sample_data():
    """Add sample community data"""
    db = SessionLocal()
    try:
        # Get existing users
        users = db.query(User).all()
        if len(users) < 2:
            logger.error("Need at least 2 users in database. Please create users first.")
            return
        
        demo_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not demo_user:
            logger.error("Demo user not found. Please create demo user first.")
            return
        
        # Add sample community posts
        posts_data = [
            {
                "user_id": demo_user.id,
                "content": "Just completed my first 5K run! 🏃‍♀️ Feeling amazing and ready for the next challenge!",
                "post_type": "achievement",
                "likes_count": 24,
                "comments_count": 8
            },
            {
                "user_id": users[1].id if len(users) > 1 else demo_user.id,
                "content": "New personal record on bench press! 💪 Thanks to everyone who motivated me to push harder.",
                "post_type": "workout",
                "likes_count": 31,
                "comments_count": 12
            },
            {
                "user_id": demo_user.id,
                "content": "Morning yoga session complete! Starting the day with mindfulness and movement. 🧘‍♀️",
                "post_type": "general",
                "likes_count": 18,
                "comments_count": 5
            }
        ]
        
        for post_data in posts_data:
            # Check if post already exists
            existing_post = db.query(CommunityPost).filter(
                CommunityPost.content == post_data["content"]
            ).first()
            
            if not existing_post:
                post = CommunityPost(**post_data)
                db.add(post)
                logger.info(f"Added community post: {post_data['content'][:50]}...")
        
        # Add sample challenges
        challenges_data = [
            {
                "title": "30-Day Fitness Challenge",
                "description": "Complete 30 workouts in 30 days to build a consistent fitness habit",
                "challenge_type": "monthly",
                "goal_type": "workouts",
                "target_value": 30,
                "unit": "workouts",
                "start_date": datetime.now(),
                "end_date": datetime.now() + timedelta(days=30),
                "reward_points": 500,
                "reward_badge": "Consistency Champion",
                "is_active": True,
                "created_by": demo_user.id
            },
            {
                "title": "Weekly Step Challenge",
                "description": "Walk 50,000 steps this week and boost your cardiovascular health",
                "challenge_type": "weekly",
                "goal_type": "distance",
                "target_value": 50000,
                "unit": "steps",
                "start_date": datetime.now(),
                "end_date": datetime.now() + timedelta(days=7),
                "reward_points": 200,
                "reward_badge": "Step Master",
                "is_active": True,
                "created_by": demo_user.id
            },
            {
                "title": "Strength Training Streak",
                "description": "Complete 5 strength training sessions to build muscle and power",
                "challenge_type": "custom",
                "goal_type": "workouts",
                "target_value": 5,
                "unit": "strength_workouts",
                "start_date": datetime.now(),
                "end_date": datetime.now() + timedelta(days=14),
                "reward_points": 300,
                "reward_badge": "Strength Warrior",
                "is_active": True,
                "created_by": demo_user.id
            }
        ]
        
        for challenge_data in challenges_data:
            # Check if challenge already exists
            existing_challenge = db.query(Challenge).filter(
                Challenge.title == challenge_data["title"]
            ).first()
            
            if not existing_challenge:
                challenge = Challenge(**challenge_data)
                db.add(challenge)
                logger.info(f"Added challenge: {challenge_data['title']}")
        
        # Commit the changes
        db.commit()
        
        # Add sample challenge participants
        challenges = db.query(Challenge).all()
        for challenge in challenges:
            # Add demo user as participant with some progress
            existing_participant = db.query(ChallengeParticipant).filter(
                ChallengeParticipant.challenge_id == challenge.id,
                ChallengeParticipant.user_id == demo_user.id
            ).first()
            
            if not existing_participant:
                participant = ChallengeParticipant(
                    challenge_id=challenge.id,
                    user_id=demo_user.id,
                    current_progress=challenge.target_value * 0.3,  # 30% progress
                    is_completed=False
                )
                db.add(participant)
                logger.info(f"Added demo user to challenge: {challenge.title}")
        
        # Add sample friendships
        if len(users) > 1:
            for i, user in enumerate(users[1:3]):  # Add first 2 other users as friends
                existing_friendship = db.query(Friendship).filter(
                    ((Friendship.user_id == demo_user.id) & (Friendship.friend_id == user.id)) |
                    ((Friendship.user_id == user.id) & (Friendship.friend_id == demo_user.id))
                ).first()
                
                if not existing_friendship:
                    friendship = Friendship(
                        user_id=demo_user.id,
                        friend_id=user.id,
                        status="accepted"
                    )
                    db.add(friendship)
                    logger.info(f"Added friendship: {demo_user.full_name} <-> {user.full_name}")
        
        # Commit all changes
        db.commit()
        logger.info("Sample community data added successfully!")
        
    except Exception as e:
        logger.error(f"Error adding sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    add_sample_data()
