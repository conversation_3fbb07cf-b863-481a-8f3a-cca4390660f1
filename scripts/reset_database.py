#!/usr/bin/env python3
"""
WibeFit Database Reset Script

This script completely cleans the database and resets all sequences.
Use this when you want to start fresh with no data.

Usage:
    python3 backend/scripts/reset_database.py

Requirements:
    - PostgreSQL server running
    - Database 'wibefit_db' exists
    - Proper database credentials configured
"""

import sys
import os
import subprocess
from pathlib import Path

def run_sql_script():
    """Run the SQL cleanup script"""
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent
    sql_script = script_dir / "clean_database.sql"
    
    if not sql_script.exists():
        print(f"❌ SQL script not found: {sql_script}")
        return False
    
    try:
        # Run the SQL script using psql
        cmd = [
            "psql", 
            "-h", "localhost", 
            "-U", "postgres", 
            "-d", "wibefit_db", 
            "-f", str(sql_script)
        ]
        
        print("🧹 Cleaning database...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database cleaned successfully!")
            print("\n📊 Cleanup Results:")
            print(result.stdout)
            return True
        else:
            print("❌ Database cleanup failed!")
            print("Error:", result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ psql command not found. Please ensure PostgreSQL is installed and in your PATH.")
        return False
    except Exception as e:
        print(f"❌ Error running cleanup script: {e}")
        return False

def main():
    """Main function"""
    print("🗃️  WibeFit Database Reset Tool")
    print("=" * 40)
    
    # Confirm with user
    response = input("⚠️  This will DELETE ALL DATA in the database. Continue? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Operation cancelled.")
        return
    
    # Run the cleanup
    if run_sql_script():
        print("\n🎉 Database reset complete!")
        print("📝 Next steps:")
        print("   1. Restart your backend server")
        print("   2. Register new users through your app")
        print("   3. All new users will start with ID 1")
    else:
        print("\n💥 Database reset failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
