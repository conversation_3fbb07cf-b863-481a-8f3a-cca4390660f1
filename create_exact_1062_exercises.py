#!/usr/bin/env python3
"""
Create exactly 1062 exercises in the database
"""

import sqlite3
import json
import re

# Database file
DB_FILE = 'wibefit.db'

def create_exactly_1062_exercises():
    """Create exactly 1062 exercises from the extracted_exercises.txt file"""
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Delete all exercises
        cursor.execute("DELETE FROM exercises")
        
        # Read the exercises file
        with open('../extracted_exercises.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        exercises_added = 0
        target_count = 1062
        
        for line in lines:
            if exercises_added >= target_count:
                break
                
            line = line.strip()
            if not line:
                continue
            
            # Parse line format: "number. Exercise Name"
            match = re.match(r'^\d+\.\s*(.+)$', line)
            if not match:
                continue
            
            exercise_name = match.group(1).strip()
            if not exercise_name:
                continue
            
            # Create slug
            slug = exercise_name.lower()
            slug = re.sub(r'[^a-z0-9\s-]', '', slug)
            slug = re.sub(r'\s+', '-', slug)
            slug = re.sub(r'-+', '-', slug)
            slug = slug.strip('-')
            
            # Handle duplicate slugs
            original_slug = slug
            counter = 1
            while True:
                cursor.execute("SELECT COUNT(*) FROM exercises WHERE slug = ?", (slug,))
                if cursor.fetchone()[0] == 0:
                    break
                slug = f"{original_slug}-{counter}"
                counter += 1
            
            # Categorize exercise
            name_lower = exercise_name.lower()
            if any(word in name_lower for word in ['ab', 'abs', 'crunch', 'sit-up', 'plank', 'twist', 'oblique']):
                category = 'core'
                muscle_groups = ['core', 'abs']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['back', 'row', 'pull', 'lat', 'deadlift']):
                category = 'back'
                muscle_groups = ['back']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['chest', 'push', 'press', 'fly', 'pec']):
                category = 'chest'
                muscle_groups = ['chest']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['leg', 'squat', 'lunge', 'calf', 'thigh']):
                category = 'legs'
                muscle_groups = ['legs']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['shoulder', 'deltoid', 'raise', 'shrug']):
                category = 'shoulders'
                muscle_groups = ['shoulders']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['arm', 'bicep', 'tricep', 'curl']):
                category = 'arms'
                muscle_groups = ['arms']
                exercise_type = 'strength'
            elif any(word in name_lower for word in ['bike', 'run', 'jump', 'cardio']):
                category = 'cardio'
                muscle_groups = ['full_body']
                exercise_type = 'cardio'
            else:
                category = 'strength'
                muscle_groups = ['full_body']
                exercise_type = 'strength'
            
            # Determine equipment
            equipment = []
            if any(word in name_lower for word in ['dumbbell', 'db']):
                equipment.append('dumbbells')
            elif any(word in name_lower for word in ['barbell', 'bb']):
                equipment.append('barbells')
            elif any(word in name_lower for word in ['machine', 'cable']):
                equipment.append('gym_equipment')
            elif any(word in name_lower for word in ['ball', 'swiss']):
                equipment.append('exercise_ball')
            elif any(word in name_lower for word in ['band', 'resistance']):
                equipment.append('resistance_bands')
            else:
                equipment.append('bodyweight')
            
            # Insert exercise
            cursor.execute('''
                INSERT INTO exercises (
                    name, slug, description, muscle_groups, equipment, difficulty,
                    exercise_type, category, duration_minutes, calories_per_minute,
                    instructions, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                exercise_name,
                slug,
                f"Effective {category} exercise targeting {', '.join(muscle_groups)}",
                json.dumps(muscle_groups),
                json.dumps(equipment),
                'beginner',
                exercise_type,
                category,
                5,
                6.0,
                f"Perform {exercise_name} with proper form and controlled movement",
                1
            ))
            
            exercises_added += 1
            
            if exercises_added % 100 == 0:
                print(f"✅ Added {exercises_added} exercises...")
        
        conn.commit()
        
        # Get final count
        cursor.execute("SELECT COUNT(*) FROM exercises")
        final_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"🎉 Exercise creation completed!")
        print(f"📊 Final exercises count: {final_count}")
        print(f"🎯 Target was: {target_count}")
        
        return final_count == target_count
        
    except Exception as e:
        print(f"❌ Error creating exercises: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Creating exactly 1062 exercises...")
    
    if create_exactly_1062_exercises():
        print("✅ Exercise creation completed successfully!")
    else:
        print("❌ Exercise creation failed!")
