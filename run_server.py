"""
WibeFit Backend Server
Run this file to start the FastAPI server
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.api import auth, workouts

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="Backend API for WibeFit - AI-powered fitness companion",
    version=settings.version,
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix="/api")
app.include_router(workouts.router, prefix="/api")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": f"{settings.app_name} is running!",
        "version": settings.version,
        "environment": settings.environment
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.version}


if __name__ == "__main__":
    uvicorn.run(
        "run_server:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
