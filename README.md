# WibeFit Backend API

Backend API for WibeFit - AI-powered fitness companion with PostgreSQL, OpenRouter AI, and Google Cloud Storage.

## 🚀 Features

### Core Features
- **User Management**: Complete user profiles, goals, and achievements
- **Exercise Library**: Comprehensive exercise database with videos and instructions
- **Workout System**: Custom workout plans, templates, and session tracking
- **Progress Analytics**: Detailed progress tracking and analytics
- **Community Features**: Social feed, challenges, and friend system
- **AI Integration**: OpenRouter AI for workout recommendations and form analysis

### Technical Features
- **Database**: PostgreSQL with SQLAlchemy ORM
- **AI/LLM**: OpenRouter integration for intelligent features
- **File Storage**: Google Cloud Storage for videos and images
- **API Documentation**: Automatic OpenAPI/Swagger documentation
- **Database Migrations**: Alembic for schema management
- **Authentication**: Ready for Firebase Auth integration

## 🏗️ Tech Stack

- **Framework**: FastAPI 0.104.1
- **Database**: PostgreSQL with SQLAlchemy 2.0
- **AI Service**: OpenRouter (Claude, GPT, etc.)
- **File Storage**: Google Cloud Storage
- **Migrations**: Alembic
- **Validation**: Pydantic
- **Testing**: pytest

## 🛠️ Quick Setup

### 1. Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

# Or run the setup script
python setup.py
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 3. Database Setup
```bash
# Create initial migration
python -m alembic revision --autogenerate -m "Initial migration"

# Apply migrations
python -m alembic upgrade head
```

### 4. Start the Server
```bash
# Development server
python main.py

# Or with uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## ⚙️ Configuration

### Environment Variables (.env)

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/wibefit_db

# Google Cloud Storage
GCS_BUCKET_NAME=wibefit-videos
GCS_PROJECT_ID=your-gcs-project-id
GOOGLE_APPLICATION_CREDENTIALS=./gcs_credentials.json

# OpenRouter
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# App Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
HOST=0.0.0.0
PORT=8000
```

### Required Services

1. **PostgreSQL Database**
   - Install PostgreSQL locally or use cloud service
   - Create database: `createdb wibefit_db`

2. **Google Cloud Storage**
   - Create GCS bucket for video storage
   - Download service account credentials
   - Place credentials file in project root

3. **OpenRouter Account**
   - Sign up at https://openrouter.ai
   - Get API key for AI features

## 🔌 API Endpoints

### Health & Status
- `GET /` - API status and version
- `GET /health` - Detailed health check

### Mock Endpoints (Current)
- `GET /api/exercises` - Exercise library
- `GET /api/workouts` - Workout plans
- `GET /api/analytics/overview` - Analytics data

### Planned API Structure
```
/api/v1/
├── auth/          # Authentication (Firebase)
├── users/         # User management
├── exercises/     # Exercise library
├── workouts/      # Workout management
├── analytics/     # Progress tracking
├── community/     # Social features
├── ai/           # AI-powered features
└── uploads/      # File uploads
```

## 🤖 AI Features (OpenRouter)

### Implemented Services
- **Workout Generation**: Personalized workout plans
- **Form Analysis**: Exercise form feedback
- **Nutrition Advice**: Dietary recommendations
- **Progress Insights**: Data-driven insights

### Usage Example
```python
from app.core.openrouter import openrouter_service

# Generate workout plan
workout = await openrouter_service.generate_workout_plan(
    user_profile={"age": 25, "fitness_level": "intermediate"},
    preferences={"duration": "30 min", "equipment": "bodyweight"}
)
```

## 📁 File Storage (Google Cloud Storage)

### Supported File Types
- **Videos**: Exercise instructions, user progress videos
- **Images**: Profile pictures, exercise images, progress photos

### Usage Example
```python
from app.core.gcs_storage import gcs_service

# Upload exercise video
video_url = gcs_service.upload_exercise_video(
    file_data=video_file,
    filename="squat_demo.mp4",
    exercise_id="123"
)
```

## 📚 Documentation

- **API Docs**: http://localhost:8000/docs (Swagger UI)
- **ReDoc**: http://localhost:8000/redoc (Alternative docs)
- **OpenAPI JSON**: http://localhost:8000/openapi.json

The API will be available at `http://localhost:8000`

## API Documentation

Once the server is running, visit:
- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc

## Project Structure

```
backend/
├── app/
│   ├── api/                 # API route handlers
│   │   ├── auth.py         # Authentication endpoints
│   │   └── workouts.py     # Workout endpoints
│   ├── core/               # Core functionality
│   │   ├── config.py       # Configuration settings
│   │   └── security.py     # Security utilities
│   ├── models/             # Database models
│   │   ├── user.py         # User models
│   │   └── workout.py      # Workout models
│   ├── schemas/            # Pydantic schemas
│   │   ├── user.py         # User schemas
│   │   └── workout.py      # Workout schemas
│   └── services/           # Business logic
├── tests/                  # Test files
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
└── run_server.py          # Server entry point
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Workouts
- `GET /api/workouts/exercises` - Get exercises
- `GET /api/workouts/exercises/{id}` - Get specific exercise
- `GET /api/workouts/plans` - Get workout plans
- `GET /api/workouts/plans/{id}` - Get specific workout plan
- `POST /api/workouts/sessions` - Start workout session
- `GET /api/workouts/sessions/my` - Get user's workout sessions

## Demo Credentials

For testing purposes, use these demo accounts:

- **Member**: `<EMAIL>` / `wibefit123`
- **Trainer**: `<EMAIL>` / `trainer123`
- **Admin**: `<EMAIL>` / `admin123`

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black .
flake8 .
```

### Database Migrations
```bash
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## Production Deployment

1. Set up PostgreSQL database
2. Configure environment variables
3. Set up Redis for caching
4. Use a production WSGI server like Gunicorn
5. Set up reverse proxy with Nginx
6. Configure SSL certificates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request
