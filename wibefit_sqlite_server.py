#!/usr/bin/env python3
"""
WibeFit SQLite Backend Server
Simple local database server for development and testing
"""

import json
import sqlite3
import hashlib
import secrets
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

# Password hashing utilities
def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
    return f"{salt}:{password_hash}"

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        salt, stored_hash = hashed_password.split(':')
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return password_hash == stored_hash
    except ValueError:
        return False

# SQLite database setup
DB_FILE = 'wibefit.db'

def init_sqlite_database():
    """Initialize SQLite database with required tables"""
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                username TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                password_hash TEXT,
                role TEXT DEFAULT 'user',
                bio TEXT,
                profile_picture TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login_at TIMESTAMP,
                metadata TEXT DEFAULT '{}'
            )
        ''')

        # Community posts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS community_posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL REFERENCES users(username),
                content TEXT NOT NULL,
                post_type TEXT DEFAULT 'general',
                likes_count INTEGER DEFAULT 0,
                comments_count INTEGER DEFAULT 0,
                media_urls TEXT DEFAULT '[]',
                is_public BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Challenges table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS challenges (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                challenge_type TEXT DEFAULT 'fitness',
                start_date TIMESTAMP,
                end_date TIMESTAMP,
                participants_count INTEGER DEFAULT 0,
                reward_points INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_by TEXT REFERENCES users(username),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # User profiles table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_profiles (
                username TEXT PRIMARY KEY REFERENCES users(username) ON DELETE CASCADE,
                age INTEGER,
                phone TEXT,
                gender TEXT,
                height REAL,
                current_weight REAL,
                target_weight REAL,
                fitness_level TEXT DEFAULT 'beginner',
                activity_level TEXT DEFAULT 'moderate',
                fitness_goals TEXT DEFAULT '[]',
                equipment_access TEXT DEFAULT '[]',
                workout_frequency INTEGER DEFAULT 3,
                preferred_workout_duration INTEGER DEFAULT 45,
                health_conditions TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Exercises table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exercises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                slug TEXT UNIQUE,
                description TEXT,
                muscle_groups TEXT DEFAULT '[]',
                equipment TEXT DEFAULT '[]',
                difficulty TEXT DEFAULT 'intermediate',
                exercise_type TEXT DEFAULT 'strength',
                category TEXT DEFAULT 'strength',
                duration_minutes INTEGER DEFAULT 5,
                calories_per_minute REAL DEFAULT 5.0,
                video_url TEXT,
                image_url TEXT,
                instructions TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Workout logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workout_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL REFERENCES users(username),
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration_minutes INTEGER,
                calories_burned INTEGER,
                exercises TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Insert demo users with passwords
        demo_password_hash = hash_password('demo123')
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, full_name, password_hash, role, bio) VALUES
            ('demo', '<EMAIL>', 'Demo User', ?, 'user', 'Demo user for testing'),
            ('testuser', '<EMAIL>', 'Test User', ?, 'user', 'Test user'),
            ('admin', '<EMAIL>', 'Admin User', ?, 'admin', 'Administrator'),
            ('fitness_guru', '<EMAIL>', 'Alex Johnson', ?, 'user', 'Certified personal trainer'),
            ('yoga_master', '<EMAIL>', 'Sarah Chen', ?, 'user', 'Yoga instructor'),
            ('cardio_king', '<EMAIL>', 'Mike Rodriguez', ?, 'user', 'Marathon runner')
        ''', (demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash))

        # Insert sample user profiles
        cursor.execute('''
            INSERT OR IGNORE INTO user_profiles (username, age, gender, height, current_weight, target_weight, fitness_level, activity_level, fitness_goals, equipment_access, workout_frequency) VALUES
            ('demo', 28, 'male', 175.0, 75.0, 70.0, 'intermediate', 'moderate', '["weight_loss", "muscle_gain"]', '["dumbbells", "resistance_bands"]', 4),
            ('sai', 25, 'male', 170.0, 70.0, 65.0, 'beginner', 'moderate', '["weight_loss", "fitness"]', '["bodyweight", "dumbbells"]', 3),
            ('testuser', 30, 'female', 165.0, 60.0, 55.0, 'intermediate', 'active', '["muscle_gain", "strength"]', '["gym_equipment", "dumbbells"]', 5),
            ('fitness_guru', 35, 'male', 180.0, 80.0, 75.0, 'advanced', 'very_active', '["strength", "muscle_gain"]', '["gym_equipment", "barbells", "dumbbells"]', 6),
            ('yoga_master', 32, 'female', 168.0, 58.0, 58.0, 'intermediate', 'active', '["flexibility", "mindfulness"]', '["yoga_mat", "blocks"]', 4),
            ('cardio_king', 29, 'male', 178.0, 72.0, 70.0, 'advanced', 'very_active', '["endurance", "weight_loss"]', '["treadmill", "bike"]', 5)
        ''')

        # Create user sessions table for token mapping
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                token TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')

        # Create roles table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                description TEXT,
                permissions TEXT DEFAULT '[]',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create user_roles table for many-to-many relationship
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                role_name TEXT NOT NULL,
                is_default BOOLEAN DEFAULT 0,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                assigned_by TEXT,
                FOREIGN KEY (role_name) REFERENCES roles (role_name),
                UNIQUE(username, role_name)
            )
        ''')

        # Insert default roles
        cursor.execute('''
            INSERT OR IGNORE INTO roles (role_name, display_name, description, permissions) VALUES
            ('app_admin', 'App Administrator', 'Full system access and control', '["all"]'),
            ('gym_admin', 'Gym Administrator', 'Manage gym operations and trainers', '["gym_management", "trainer_management", "member_view"]'),
            ('trainer', 'Trainer', 'Manage clients and workout programs', '["client_management", "workout_creation", "progress_tracking"]'),
            ('member', 'Member', 'Access workouts and community features', '["workout_access", "community_access", "profile_management"]'),
            ('visitor', 'Visitor', 'Limited access to public content', '["public_access"]')
        ''')

        # Assign default roles to existing users
        cursor.execute('''
            INSERT OR IGNORE INTO user_roles (username, role_name, is_default) VALUES
            ('sai', 'app_admin', 1),
            ('sai', 'member', 0),
            ('demo_user_12345', 'member', 1),
            ('demo_user_67890', 'member', 1),
            ('john_doe', 'trainer', 1),
            ('jane_smith', 'member', 1),
            ('mike_wilson', 'member', 1)
        ''')

        # Insert sample exercises
        cursor.execute('''
            INSERT OR IGNORE INTO exercises (name, slug, description, muscle_groups, equipment, difficulty, exercise_type, category, duration_minutes, calories_per_minute, instructions) VALUES
            ('Push-ups', 'push-ups', 'Classic bodyweight exercise for chest, shoulders, and triceps', '["chest", "shoulders", "triceps"]', '["bodyweight"]', 'beginner', 'strength', 'upper_body', 5, 6.0, 'Start in plank position, lower body to ground, push back up'),
            ('Squats', 'squats', 'Fundamental lower body exercise targeting quads, glutes, and hamstrings', '["quadriceps", "glutes", "hamstrings"]', '["bodyweight"]', 'beginner', 'strength', 'lower_body', 5, 7.0, 'Stand with feet shoulder-width apart, lower hips back and down, return to standing'),
            ('Plank', 'plank', 'Core strengthening exercise that also engages shoulders and glutes', '["core", "shoulders", "glutes"]', '["bodyweight"]', 'beginner', 'strength', 'core', 3, 4.0, 'Hold straight line from head to heels, engage core muscles'),
            ('Burpees', 'burpees', 'Full-body cardio exercise combining squat, plank, and jump', '["full_body"]', '["bodyweight"]', 'intermediate', 'cardio', 'full_body', 5, 10.0, 'Squat down, jump back to plank, do push-up, jump feet forward, jump up'),
            ('Mountain Climbers', 'mountain-climbers', 'Dynamic cardio exercise targeting core and legs', '["core", "legs", "shoulders"]', '["bodyweight"]', 'intermediate', 'cardio', 'cardio', 4, 8.0, 'Start in plank, alternate bringing knees to chest rapidly'),
            ('Lunges', 'lunges', 'Single-leg exercise for lower body strength and balance', '["quadriceps", "glutes", "hamstrings"]', '["bodyweight"]', 'beginner', 'strength', 'lower_body', 5, 6.0, 'Step forward, lower back knee toward ground, return to standing'),
            ('Jumping Jacks', 'jumping-jacks', 'Classic cardio exercise for full-body warm-up', '["full_body"]', '["bodyweight"]', 'beginner', 'cardio', 'cardio', 3, 7.0, 'Jump feet apart while raising arms overhead, return to starting position'),
            ('Dumbbell Bicep Curls', 'dumbbell-bicep-curls', 'Isolation exercise for bicep development', '["biceps"]', '["dumbbells"]', 'beginner', 'strength', 'upper_body', 4, 5.0, 'Hold dumbbells at sides, curl up to shoulders, lower slowly'),
            ('Dumbbell Shoulder Press', 'dumbbell-shoulder-press', 'Overhead pressing movement for shoulder strength', '["shoulders", "triceps"]', '["dumbbells"]', 'intermediate', 'strength', 'upper_body', 5, 6.0, 'Press dumbbells from shoulder height to overhead, lower slowly'),
            ('Deadlifts', 'deadlifts', 'Compound exercise for posterior chain development', '["hamstrings", "glutes", "back"]', '["dumbbells", "barbells"]', 'intermediate', 'strength', 'full_body', 6, 8.0, 'Hinge at hips, lower weight toward ground, drive hips forward to stand')
        ''')

        # Insert sample community posts
        cursor.execute('''
            INSERT OR IGNORE INTO community_posts (username, content, post_type, likes_count, comments_count) VALUES
            ('fitness_guru', 'Just completed an amazing 45-minute HIIT workout! 💪 Feeling stronger every day!', 'workout', 24, 8),
            ('yoga_master', 'Morning yoga session complete! 🧘‍♀️ Starting the day with mindfulness and movement.', 'general', 18, 5),
            ('cardio_king', 'New personal record! 🏃‍♂️ Just ran 10K in under 45 minutes!', 'achievement', 32, 12)
        ''')

        # Insert sample challenges
        cursor.execute('''
            INSERT OR IGNORE INTO challenges (title, description, challenge_type, participants_count, reward_points, created_by) VALUES
            ('30-Day Fitness Challenge', 'Complete 30 workouts in 30 days', 'fitness', 156, 500, 'admin'),
            ('Weekly Step Challenge', 'Walk 10,000 steps every day this week', 'cardio', 89, 200, 'admin'),
            ('Strength Training Month', 'Focus on strength training for the entire month', 'strength', 67, 400, 'admin')
        ''')

        # Insert sample workout logs
        cursor.execute('''
            INSERT OR IGNORE INTO workout_logs (username, duration_minutes, calories_burned, exercises) VALUES
            ('demo', 45, 380, '[{"name": "HIIT Workout", "duration": 45}]'),
            ('fitness_guru', 60, 450, '[{"name": "Strength Training", "duration": 60}]'),
            ('yoga_master', 30, 200, '[{"name": "Yoga Flow", "duration": 30}]')
        ''')

        conn.commit()
        conn.close()
        print("✅ SQLite database initialized successfully")
        return True

    except Exception as e:
        print(f"❌ SQLite database error: {e}")
        return False

class WibeFitSQLiteHandler(BaseHTTPRequestHandler):
    """HTTP request handler for WibeFit SQLite backend"""

    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()

    def do_PUT(self):
        """Handle PUT requests"""
        if self.path == '/api/users/me/profile':
            self.handle_users_me_profile_put()
        else:
            self.send_error(501, "Unsupported method ('PUT')")

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    def get_current_user_from_auth(self):
        """Extract current user from Authorization header or fallback methods"""
        try:
            # Try to get username from Authorization header
            auth_header = self.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                print(f'🔍 Found auth token: {token[:20]}...')

                # Handle different token types
                if 'demo_token' in token:
                    # Extract timestamp and create demo username
                    timestamp = token.split('_')[-1] if '_' in token else '1'
                    username = f'demo_user_{timestamp}'
                    print(f'👤 Extracted demo user: {username}')
                    return username
                elif 'registered_token' in token:
                    # For registration tokens, extract timestamp and check database
                    timestamp = token.split('_')[-1] if '_' in token else '1'
                    # Try to find the most recently registered user
                    username = self._get_latest_registered_user()
                    print(f'👤 Using latest registered user: {username}')
                    return username
                elif 'token_' in token:
                    # For simple tokens, extract timestamp and find user
                    timestamp = token.split('_')[-1] if '_' in token else '1'
                    username = self._get_user_by_token_timestamp(timestamp)
                    print(f'👤 Found user by token timestamp: {username}')
                    return username
                else:
                    # For other tokens, check user sessions table
                    username = self._get_user_from_session_table(token)
                    if username:
                        print(f'👤 Found user from session: {username}')
                        return username
                    else:
                        # Fallback to default user
                        username = 'sai'
                        print(f'👤 Using default authenticated user: {username}')
                        return username

            # Try to get username from query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if 'username' in query_params:
                username = query_params['username'][0]
                print(f'👤 Found username in query: {username}')
                return username

            # Default fallback to 'sai' for backward compatibility
            print('⚠️ No user authentication found, using default user: sai')
            return 'sai'

        except Exception as e:
            print(f'❌ Error extracting user from auth: {e}')
            return 'sai'

    def _get_latest_registered_user(self):
        """Get the most recently registered user"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute('SELECT username FROM users ORDER BY created_at DESC LIMIT 1')
            result = cursor.fetchone()
            conn.close()
            return result[0] if result else 'sai'
        except:
            return 'sai'

    def _get_user_by_token_timestamp(self, timestamp):
        """Try to find user by token timestamp (simplified approach)"""
        try:
            # For demo purposes, map common timestamps to known users
            # In production, you'd store token-user mappings in database
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute('SELECT username FROM users WHERE username != "demo" ORDER BY created_at DESC LIMIT 1')
            result = cursor.fetchone()
            conn.close()
            return result[0] if result else 'sai'
        except:
            return 'sai'

    def _get_user_from_session_table(self, token):
        """Get username from user sessions table"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT username FROM user_sessions
                WHERE token = ? AND is_active = 1
                AND (expires_at IS NULL OR expires_at > datetime('now'))
            ''', (token,))
            result = cursor.fetchone()
            conn.close()
            return result[0] if result else None
        except:
            return None

    def _store_user_session(self, token, username):
        """Store token-username mapping in sessions table"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO user_sessions (token, username, expires_at)
                VALUES (?, ?, datetime('now', '+24 hours'))
            ''', (token, username))
            conn.commit()
            conn.close()
            print(f'💾 Stored session: {token[:20]}... -> {username}')
        except Exception as e:
            print(f'❌ Failed to store session: {e}')

    def _get_user_profile_from_db(self, username):
        """Fetch user profile from database"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT age, gender, height, current_weight, target_weight,
                       fitness_level, activity_level, fitness_goals,
                       equipment_access, workout_frequency, preferred_workout_duration
                FROM user_profiles WHERE username = ?
            ''', (username,))
            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'age': result[0],
                    'gender': result[1],
                    'height': result[2],
                    'current_weight': result[3],
                    'target_weight': result[4],
                    'fitness_level': result[5],
                    'activity_level': result[6],
                    'fitness_goals': json.loads(result[7]) if result[7] else [],
                    'equipment_access': json.loads(result[8]) if result[8] else [],
                    'workout_frequency': result[9],
                    'preferred_workout_duration': result[10]
                }
            else:
                print(f'⚠️ No profile found for user: {username}')
                return {}
        except Exception as e:
            print(f'❌ Error fetching user profile: {e}')
            return {}

    def do_GET(self):
        """Handle GET requests"""
        
        if self.path == '/':
            self.handle_root()
        elif self.path == '/health':
            self.handle_health()
        elif self.path.startswith('/api/community/posts'):
            self.handle_community_posts_get()
        elif self.path.startswith('/api/community/challenges'):
            self.handle_challenges_get()
        elif self.path.startswith('/api/community/leaderboard'):
            self.handle_leaderboard_get()
        elif self.path == '/api/auth/profile' or self.path.startswith('/api/auth/profile?'):
            self.handle_profile_get()
        elif self.path == '/api/users/me':
            self.handle_users_me_get()
        elif self.path == '/api/users/me/stats':
            self.handle_users_me_stats_get()
        elif self.path == '/api/users/me/profile':
            self.handle_users_me_profile_get()
        elif self.path.startswith('/api/community/users/'):
            self.handle_community_users_get()
        elif self.path.startswith('/api/exercises'):
            self.handle_exercises_get()
        elif self.path.startswith('/api/workouts/exercises'):
            self.handle_workouts_exercises_get()
        elif self.path == '/api/admin/users':
            self.handle_admin_users_get()
        elif self.path == '/api/admin/roles':
            self.handle_admin_roles_get()
        elif self.path.startswith('/api/admin/users/') and '/roles' in self.path:
            self.handle_admin_user_roles_get()
        elif self.path == '/api/users/me/roles':
            self.handle_user_roles_get()
        else:
            self.send_error(404, "Not Found")

    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            data = {}

        if self.path == '/api/auth/login-username':
            self.handle_login_username(data)
        elif self.path == '/api/auth/register-username':
            self.handle_register_username(data)
        elif self.path == '/api/auth/profile':
            self.handle_profile_post(data)
        elif self.path == '/api/ai/generate-workout-plan':
            self.handle_ai_workout_generation(data)
        elif self.path == '/api/admin/users':
            self.handle_admin_users_post(data)
        elif self.path.startswith('/api/admin/users/') and '/roles' in self.path:
            self.handle_admin_user_roles_post(data)
        else:
            self.send_error(404, "Not Found")

    def handle_root(self):
        """Handle root endpoint"""
        response = {
            "message": "WibeFit SQLite Backend Server",
            "version": "1.0",
            "database": "SQLite",
            "status": "running"
        }
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_health(self):
        """Handle health check endpoint"""
        response = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": "SQLite",
            "version": "1.0"
        }
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_login_username(self, data):
        """Handle POST /api/auth/login-username"""
        try:
            username = data.get('username')
            password = data.get('password')
            
            print(f"🔍 Login attempt - Username: {username}, Password length: {len(password) if password else 0}")
            
            if not username or not password:
                print("❌ Missing username or password")
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Username and password required"}).encode())
                return

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT username, email, full_name, password_hash, role, is_active
                FROM users 
                WHERE username = ?
            """, (username,))
            
            user_row = cursor.fetchone()
            conn.close()
            
            print(f"🔍 Database query result: {user_row is not None}")
            
            if not user_row:
                print(f"❌ User '{username}' not found in database")
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Invalid username or password"}).encode())
                return
            
            username_db, email, full_name, password_hash, role, is_active = user_row
            print(f"🔍 Found user: {username_db}, has_password: {password_hash is not None}, is_active: {is_active}")
            
            if not is_active:
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Account is inactive"}).encode())
                return

            if password_hash:
                password_valid = verify_password(password, password_hash)
                print(f"🔍 Password verification: {password_valid}")
                if not password_valid:
                    print(f"❌ Invalid password for user '{username}'")
                    self.send_response(401)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Invalid username or password"}).encode())
                    return
            else:
                print("⚠️ No password hash stored, accepting any password")

            # Generate access token
            access_token = secrets.token_urlsafe(32)
            
            response = {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "username": username_db,
                    "email": email,
                    "full_name": full_name,
                    "role": role
                }
            }
            
            print(f"✅ Login successful for user: {username}")
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            print(f"❌ Username login error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Login failed"}).encode())

    def handle_register_username(self, data):
        """Handle POST /api/auth/register-username"""
        try:
            username = data.get('username')
            password = data.get('password')

            print(f"🔍 Registration attempt - Username: {username}, Password length: {len(password) if password else 0}")

            if not username or not password:
                print(f"❌ Missing credentials - Username: {username}, Password: {'provided' if password else 'missing'}")
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Username and password required"}).encode())
                return

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Check if username already exists
            cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
            existing_user = cursor.fetchone()
            print(f"🔍 Username check result: {existing_user}")

            if existing_user:
                print(f"❌ Username '{username}' already exists in database")
                conn.close()
                self.send_response(409)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Username already exists"}).encode())
                return

            # Hash password and create user
            password_hash = hash_password(password)
            cursor.execute("""
                INSERT INTO users (username, email, full_name, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (username, f"{username}@wibefit.local", username.title(), password_hash, 'user', 1))

            conn.commit()
            conn.close()

            # Generate access token
            access_token = secrets.token_urlsafe(32)

            response = {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "username": username,
                    "email": f"{username}@wibefit.local",
                    "full_name": username.title(),
                    "role": "user"
                }
            }

            print(f"✅ Registration successful for user: {username}")
            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Username registration error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Registration failed"}).encode())

    def handle_community_posts_get(self):
        """Handle GET /api/community/posts"""
        try:
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            limit = int(query_params.get('limit', [20])[0])

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.id, p.username, u.full_name, p.content, p.post_type,
                       p.likes_count, p.comments_count, p.created_at, p.media_urls
                FROM community_posts p
                JOIN users u ON p.username = u.username
                WHERE p.is_public = 1
                ORDER BY p.created_at DESC
                LIMIT ?
            """, (limit,))

            posts = []
            for row in cursor.fetchall():
                posts.append({
                    'id': row[0],
                    'username': row[1],
                    'user_name': row[2],
                    'content': row[3],
                    'post_type': row[4],
                    'likes_count': row[5],
                    'comments_count': row[6],
                    'created_at': row[7],
                    'media_urls': json.loads(row[8]) if row[8] else [],
                    'user_liked': False
                })

            conn.close()

            response = {'posts': posts}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Community posts error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch community posts"}).encode())

    def handle_profile_get(self):
        """Handle GET /api/auth/profile"""
        try:
            # Try to get username from query parameter first, then from auth
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            if 'username' in query_params:
                username = query_params['username'][0]
                print(f'🔍 Using username from query: {username}')
            else:
                username = self.get_current_user_from_auth()
                print(f'🔍 Using username from auth: {username}')

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get user profile from database
            cursor.execute('''
                SELECT age, phone, gender, height, current_weight, target_weight,
                       fitness_level, activity_level, fitness_goals, equipment_access,
                       workout_frequency, preferred_workout_duration, health_conditions
                FROM user_profiles WHERE username = ?
            ''', (username,))

            profile_data = cursor.fetchone()
            conn.close()

            if profile_data:
                profile = {
                    "age": profile_data[0],
                    "phone": profile_data[1],
                    "gender": profile_data[2],
                    "height": profile_data[3],
                    "current_weight": profile_data[4],
                    "target_weight": profile_data[5],
                    "fitness_level": profile_data[6],
                    "activity_level": profile_data[7],
                    "primary_goals": json.loads(profile_data[8]) if profile_data[8] else [],
                    "equipment_access": json.loads(profile_data[9]) if profile_data[9] else [],
                    "preferred_workout_days": profile_data[10],
                    "preferred_workout_duration": profile_data[11],
                    "health_conditions": json.loads(profile_data[12]) if profile_data[12] else []
                }
            else:
                # Return empty profile if none exists - no fake data
                profile = {
                    "age": None,
                    "phone": None,
                    "gender": None,
                    "height": None,
                    "current_weight": None,
                    "target_weight": None,
                    "fitness_level": None,
                    "activity_level": None,
                    "primary_goals": [],
                    "equipment_access": [],
                    "preferred_workout_days": None,
                    "preferred_workout_duration": None,
                    "health_conditions": [],
                    "profile_complete": False
                }

            response = {
                "profile": profile,
                "status": "success"
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Auth profile returned for user: {username}")

        except Exception as e:
            print(f"❌ Profile error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch profile"}).encode())

    def handle_profile_post(self, data):
        """Handle POST /api/auth/profile - Create/Update user profile"""
        try:
            print(f"🔍 Profile creation request: {data}")

            # For now, just return success - in a real app you'd save to database
            response = {
                "message": "Profile created successfully",
                "status": "success"
            }

            print(f"✅ Profile creation successful")
            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Profile creation error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to create profile"}).encode())

    def handle_users_me_get(self):
        """Handle GET /api/users/me"""
        try:
            # Get current authenticated user
            username = self.get_current_user_from_auth()
            print(f'🔍 Getting user data for: {username}')

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get user data from database
            cursor.execute('''
                SELECT username, email, full_name, role, bio, profile_picture,
                       is_active, created_at, last_login_at, metadata
                FROM users WHERE username = ?
            ''', (username,))

            user_data = cursor.fetchone()
            conn.close()

            if user_data:
                response = {
                    "id": 1,  # For compatibility
                    "username": user_data[0],
                    "email": user_data[1],
                    "full_name": user_data[2],
                    "status": "success"
                }
            else:
                # Return default if user not found
                response = {
                    "id": 1,
                    "username": username,
                    "email": f"{username}@wibefit.local",
                    "full_name": username.title(),
                    "status": "success"
                }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ User data returned for: {username}")

        except Exception as e:
            print(f"❌ Users me error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch user"}).encode())

    def handle_users_me_stats_get(self):
        """Handle GET /api/users/me/stats"""
        try:
            # Get current authenticated user
            username = self.get_current_user_from_auth()
            print(f'🔍 Getting stats for user: {username}')

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get workout stats from database
            cursor.execute('''
                SELECT
                    COUNT(*) as total_workouts,
                    COALESCE(SUM(calories_burned), 0) as total_calories,
                    COALESCE(SUM(duration_minutes), 0) as total_time
                FROM workout_logs WHERE username = ?
            ''', (username,))

            stats_data = cursor.fetchone()
            conn.close()

            if stats_data:
                response = {
                    "total_workouts": stats_data[0],
                    "total_calories": stats_data[1],
                    "total_time": stats_data[2],
                    "streak": 0,  # Could be calculated based on workout dates
                    "status": "success"
                }
            else:
                response = {
                    "total_workouts": 0,
                    "total_calories": 0,
                    "total_time": 0,
                    "streak": 0,
                    "status": "success"
                }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ User stats returned for: {username}")

        except Exception as e:
            print(f"❌ Users stats error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch stats"}).encode())

    def handle_users_me_profile_get(self):
        """Handle GET /api/users/me/profile"""
        try:
            # Get current authenticated user
            username = self.get_current_user_from_auth()
            print(f'🔍 Getting profile for user: {username}')

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get user profile from database
            cursor.execute('''
                SELECT age, phone, gender, height, current_weight, target_weight,
                       fitness_level, activity_level, fitness_goals, equipment_access,
                       workout_frequency, preferred_workout_duration, health_conditions
                FROM user_profiles WHERE username = ?
            ''', (username,))

            profile_data = cursor.fetchone()
            conn.close()

            if profile_data:
                response = {
                    "age": profile_data[0],
                    "phone": profile_data[1],
                    "gender": profile_data[2],
                    "height": profile_data[3],
                    "current_weight": profile_data[4],
                    "target_weight": profile_data[5],
                    "fitness_level": profile_data[6],
                    "activity_level": profile_data[7],
                    "primary_goals": json.loads(profile_data[8]) if profile_data[8] else [],
                    "equipment_access": json.loads(profile_data[9]) if profile_data[9] else [],
                    "preferred_workout_days": profile_data[10],
                    "preferred_workout_duration": profile_data[11],
                    "health_conditions": json.loads(profile_data[12]) if profile_data[12] else [],
                    "status": "success"
                }
            else:
                # Return empty profile if none exists - no fake data
                response = {
                    "age": None,
                    "phone": None,
                    "gender": None,
                    "height": None,
                    "current_weight": None,
                    "target_weight": None,
                    "fitness_level": None,
                    "activity_level": None,
                    "primary_goals": [],
                    "equipment_access": [],
                    "preferred_workout_days": None,
                    "preferred_workout_duration": None,
                    "health_conditions": [],
                    "profile_complete": False,
                    "status": "success"
                }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Profile returned for user: {username}")

        except Exception as e:
            print(f"❌ Users profile error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch profile"}).encode())

    def handle_users_me_profile_put(self):
        """Handle PUT /api/users/me/profile - Update user profile"""
        try:
            # Read request body
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            profile_data = json.loads(post_data.decode('utf-8'))

            print(f'🔄 Updating user profile: {profile_data}')

            # Get current authenticated user
            username = self.get_current_user_from_auth()
            print(f'🔍 Updating profile for user: {username}')

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Check if profile exists
            cursor.execute('SELECT username FROM user_profiles WHERE username = ?', (username,))
            exists = cursor.fetchone()

            if exists:
                # Update existing profile
                cursor.execute('''
                    UPDATE user_profiles SET
                        age = ?, phone = ?, gender = ?, height = ?, current_weight = ?,
                        target_weight = ?, fitness_level = ?, activity_level = ?,
                        fitness_goals = ?, equipment_access = ?, workout_frequency = ?,
                        preferred_workout_duration = ?, health_conditions = ?
                    WHERE username = ?
                ''', (
                    profile_data.get('age'),
                    profile_data.get('phone'),
                    profile_data.get('gender'),
                    profile_data.get('height'),
                    profile_data.get('current_weight'),
                    profile_data.get('target_weight'),
                    profile_data.get('fitness_level'),
                    profile_data.get('activity_level'),
                    json.dumps(profile_data.get('primary_goals', profile_data.get('fitness_goals', []))),
                    json.dumps(profile_data.get('equipment_access', [])),
                    profile_data.get('preferred_workout_days', profile_data.get('workout_frequency')),
                    profile_data.get('preferred_workout_duration'),
                    json.dumps(profile_data.get('health_conditions', [])),
                    username
                ))
                print(f'✅ Updated profile for user: {username}')
            else:
                # Create new profile
                cursor.execute('''
                    INSERT INTO user_profiles (
                        username, age, phone, gender, height, current_weight,
                        target_weight, fitness_level, activity_level, fitness_goals,
                        equipment_access, workout_frequency, preferred_workout_duration,
                        health_conditions
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    username,
                    profile_data.get('age'),
                    profile_data.get('phone'),
                    profile_data.get('gender'),
                    profile_data.get('height'),
                    profile_data.get('current_weight'),
                    profile_data.get('target_weight'),
                    profile_data.get('fitness_level'),
                    profile_data.get('activity_level'),
                    json.dumps(profile_data.get('primary_goals', profile_data.get('fitness_goals', []))),
                    json.dumps(profile_data.get('equipment_access', [])),
                    profile_data.get('preferred_workout_days', profile_data.get('workout_frequency')),
                    profile_data.get('preferred_workout_duration'),
                    json.dumps(profile_data.get('health_conditions', []))
                ))
                print(f'✅ Created new profile for user: {username}')

            conn.commit()
            conn.close()

            # Return success response
            self.send_response(200)
            self.send_cors_headers()
            self.send_header('Content-Type', 'application/json')
            self.end_headers()

            response = {
                "status": "success",
                "message": "Profile updated successfully",
                "username": username
            }
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f'❌ Error updating profile: {e}')
            self.send_response(500)
            self.send_cors_headers()
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def handle_community_users_get(self):
        """Handle GET /api/community/users/*/followers and /api/community/users/*/following"""
        try:
            response = {
                "count": 0,
                "users": [],
                "status": "success"
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            print(f"❌ Community users error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch community data"}).encode())

    def handle_exercises_get(self):
        """Handle GET /api/exercises"""
        try:
            # Parse query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            limit = int(query_params.get('limit', [20])[0])
            offset = int(query_params.get('offset', [0])[0])
            category = query_params.get('category', [None])[0]
            search = query_params.get('search', [None])[0]

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Build WHERE clause
            where_conditions = ["is_active = 1"]
            params = []

            if search:
                where_conditions.append("(name LIKE ? OR description LIKE ?)")
                params.extend([f"%{search}%", f"%{search}%"])

            if category:
                where_conditions.append("category = ?")
                params.append(category.lower())

            where_clause = " AND ".join(where_conditions)

            # Get total count
            count_query = f"SELECT COUNT(*) FROM exercises WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # Get exercises with pagination
            exercise_query = f"""
                SELECT id, name, description, muscle_groups, equipment, difficulty,
                       exercise_type, category, duration_minutes, calories_per_minute,
                       video_url, image_url, created_at
                FROM exercises
                WHERE {where_clause}
                ORDER BY name
                LIMIT ? OFFSET ?
            """
            cursor.execute(exercise_query, params + [limit, offset])

            exercises = []
            for row in cursor.fetchall():
                exercise = {
                    "id": row[0],
                    "name": row[1] or "",
                    "slug": (row[1] or "").lower().replace(" ", "-").replace("_", "-"),
                    "description": row[2] or "",
                    "muscle_groups": json.loads(row[3]) if row[3] else [],
                    "equipment": json.loads(row[4]) if row[4] else [],
                    "difficulty": row[5] or "intermediate",
                    "exercise_type": row[6] or "strength",
                    "category": row[7] or "strength",
                    "duration_minutes": row[8] or 5,
                    "calories_per_minute": row[9] or 5.0,
                    "video_url": row[10] or "",
                    "image_url": row[11] or "",
                    "is_active": True,
                    "created_at": row[12] if row[12] else None
                }
                exercises.append(exercise)

            conn.close()

            response = {
                "exercises": exercises,
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + len(exercises)) < total_count,
                "status": "success"
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Exercises returned: {len(exercises)} of {total_count} total")

        except Exception as e:
            print(f"❌ Exercises error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch exercises"}).encode())

    def handle_workouts_exercises_get(self):
        """Handle GET /api/workouts/exercises"""
        # For now, just redirect to the main exercises endpoint
        self.handle_exercises_get()

    def handle_ai_workout_generation(self, data):
        """Handle AI workout plan generation"""
        try:
            print(f"🤖 AI Workout Generation Request: {data}")

            # Get current authenticated user
            username = self.get_current_user_from_auth()
            print(f"🔍 Generating AI plan for user: {username}")

            # Fetch user profile from database
            user_profile = self._get_user_profile_from_db(username)
            plan_type = data.get('plan_type', '4_day_split')
            duration_weeks = data.get('duration_weeks', 4)

            print(f"👤 User Profile: {user_profile}")
            print(f"📋 Plan Type: {plan_type}")
            print(f"⏱️ Duration: {duration_weeks} weeks")

            # Generate workout plan based on user profile and exercises from database
            ai_plan = self.generate_workout_plan_from_database(
                user_profile=user_profile,
                plan_type=plan_type,
                duration_weeks=duration_weeks
            )

            print(f"✅ AI Plan Generated: {len(ai_plan.get('workouts', []))} workouts")

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(ai_plan).encode())

        except Exception as e:
            print(f"❌ AI Workout Generation Error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def generate_workout_plan_from_database(self, user_profile, plan_type, duration_weeks):
        """Generate intelligent workout plan using exercises from the database"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get exercises from database categorized by muscle groups
            cursor.execute('''
                SELECT id, name, description, muscle_groups, equipment, difficulty,
                       exercise_type, category, duration_minutes, calories_per_minute
                FROM exercises WHERE is_active = 1
                ORDER BY RANDOM()
            ''')

            all_exercises = []
            for row in cursor.fetchall():
                exercise = {
                    "id": row[0],
                    "name": row[1],
                    "description": row[2],
                    "muscle_groups": json.loads(row[3]) if row[3] else [],
                    "equipment": json.loads(row[4]) if row[4] else [],
                    "difficulty": row[5],
                    "exercise_type": row[6],
                    "category": row[7],
                    "duration_minutes": row[8],
                    "calories_per_minute": row[9]
                }
                all_exercises.append(exercise)

            conn.close()

            # Extract user preferences
            fitness_level = user_profile.get('fitness_level', 'beginner')
            equipment_access = user_profile.get('equipment_access') or ['bodyweight']
            preferred_workout_days = user_profile.get('preferred_workout_days') or []

            # Calculate workout days count from user preference
            if isinstance(preferred_workout_days, list):
                # If it's a list of day names, use the count
                workout_days_count = len(preferred_workout_days) if preferred_workout_days else 4
            else:
                # If it's a number, use it directly
                workout_days_count = preferred_workout_days

            # Ensure workout days is between 4-7
            workout_days_count = max(4, min(7, workout_days_count))

            print(f"🎯 Generating {workout_days_count}-day workout plan for {fitness_level} level")
            print(f"🏋️ Available equipment: {equipment_access}")

            # Filter exercises based on user's equipment and fitness level
            suitable_exercises = self._filter_exercises_by_equipment_and_level(
                all_exercises, equipment_access, fitness_level
            )

            # Index exercises by muscle groups for smart selection
            exercises_by_muscle = self._index_exercises_by_muscle_groups(suitable_exercises)

            print(f"📊 Found {len(suitable_exercises)} suitable exercises")
            print(f"🎯 Muscle groups available: {list(exercises_by_muscle.keys())}")

            # Create workout plan
            workout_plan = {
                "plan_name": f"AI-Generated {workout_days_count}-Day Plan",
                "description": f"Personalized {duration_weeks}-week, {workout_days_count}-day workout plan",
                "duration_weeks": duration_weeks,
                "plan_type": f"{workout_days_count}_day_split",
                "fitness_level": fitness_level,
                "workout_days_per_week": workout_days_count,
                "workouts": []
            }

            # Generate dynamic workout split based on days per week
            workout_days = self._generate_workout_split(workout_days_count)

            # Track used exercises to avoid repetition
            used_exercise_ids = set()

            # Generate exercises for each day with smart selection
            for day_info in workout_days:
                print(f"🏋️ Generating Day {day_info['day']}: {day_info['name']}")

                day_exercises = self._select_exercises_for_day(
                    day_info, exercises_by_muscle, used_exercise_ids, fitness_level
                )

                workout_plan["workouts"].append({
                    "day": day_info["day"],
                    "name": day_info["name"],
                    "focus_areas": day_info["focus"],
                    "estimated_duration": self._calculate_workout_duration(day_exercises),
                    "exercises": day_exercises
                })

                print(f"✅ Day {day_info['day']} complete: {len(day_exercises)} exercises")

            print(f"🎉 Generated {workout_days_count}-day workout plan with {len(workout_plan['workouts'])} workouts")
            return workout_plan

        except Exception as e:
            print(f"❌ Error generating workout plan: {e}")
            # Return a basic fallback plan
            return self._get_fallback_workout_plan(user_profile)

    def _filter_exercises_by_equipment_and_level(self, all_exercises, equipment_access, fitness_level):
        """Filter exercises based on user's equipment and fitness level"""
        suitable_exercises = []

        for exercise in all_exercises:
            # Check equipment compatibility
            exercise_equipment = exercise.get('equipment', [])

            # Handle None or empty equipment_access
            if not equipment_access:
                equipment_access = ['bodyweight']

            has_equipment = any(eq.lower() in [e.lower() for e in equipment_access] for eq in exercise_equipment)

            # If no equipment specified for exercise, assume it's bodyweight
            if not exercise_equipment:
                has_equipment = True

            # Check difficulty level
            exercise_difficulty = exercise.get('difficulty', 'beginner').lower()
            is_suitable_difficulty = True

            if fitness_level == 'beginner' and exercise_difficulty == 'advanced':
                is_suitable_difficulty = False

            if has_equipment and is_suitable_difficulty:
                suitable_exercises.append(exercise)

        return suitable_exercises

    def _index_exercises_by_muscle_groups(self, exercises):
        """Index exercises by muscle groups for smart selection"""
        exercises_by_muscle = {}

        for exercise in exercises:
            muscle_groups = exercise.get('muscle_groups', [])
            category = exercise.get('category', 'general').lower()

            # Index by category
            if category not in exercises_by_muscle:
                exercises_by_muscle[category] = []
            exercises_by_muscle[category].append(exercise)

            # Also index by individual muscle groups
            for muscle in muscle_groups:
                muscle_key = muscle.lower()
                if muscle_key not in exercises_by_muscle:
                    exercises_by_muscle[muscle_key] = []
                exercises_by_muscle[muscle_key].append(exercise)

        return exercises_by_muscle

    def _generate_workout_split(self, workout_days_count):
        """Generate dynamic workout split based on number of days"""
        if workout_days_count == 4:
            return [
                {"day": 1, "name": "Upper Body Push", "focus": ["chest", "shoulders", "triceps"], "target_muscles": ["chest", "shoulders", "arms"]},
                {"day": 2, "name": "Lower Body", "focus": ["legs", "glutes"], "target_muscles": ["legs"]},
                {"day": 3, "name": "Upper Body Pull", "focus": ["back", "biceps"], "target_muscles": ["back", "arms"]},
                {"day": 4, "name": "Core & Cardio", "focus": ["core", "cardio"], "target_muscles": ["core", "cardio"]}
            ]
        elif workout_days_count == 5:
            return [
                {"day": 1, "name": "Chest & Triceps", "focus": ["chest", "triceps"], "target_muscles": ["chest", "arms"]},
                {"day": 2, "name": "Back & Biceps", "focus": ["back", "biceps"], "target_muscles": ["back", "arms"]},
                {"day": 3, "name": "Legs", "focus": ["legs", "glutes"], "target_muscles": ["legs"]},
                {"day": 4, "name": "Shoulders & Core", "focus": ["shoulders", "core"], "target_muscles": ["shoulders", "core"]},
                {"day": 5, "name": "Full Body & Cardio", "focus": ["full_body", "cardio"], "target_muscles": ["chest", "back", "legs", "cardio"]}
            ]
        elif workout_days_count == 6:
            return [
                {"day": 1, "name": "Chest", "focus": ["chest"], "target_muscles": ["chest"]},
                {"day": 2, "name": "Back", "focus": ["back"], "target_muscles": ["back"]},
                {"day": 3, "name": "Legs", "focus": ["legs"], "target_muscles": ["legs"]},
                {"day": 4, "name": "Shoulders", "focus": ["shoulders"], "target_muscles": ["shoulders"]},
                {"day": 5, "name": "Arms", "focus": ["arms"], "target_muscles": ["arms"]},
                {"day": 6, "name": "Core & Cardio", "focus": ["core", "cardio"], "target_muscles": ["core", "cardio"]}
            ]
        else:  # 7 days
            return [
                {"day": 1, "name": "Chest", "focus": ["chest"], "target_muscles": ["chest"]},
                {"day": 2, "name": "Back", "focus": ["back"], "target_muscles": ["back"]},
                {"day": 3, "name": "Legs", "focus": ["legs"], "target_muscles": ["legs"]},
                {"day": 4, "name": "Shoulders", "focus": ["shoulders"], "target_muscles": ["shoulders"]},
                {"day": 5, "name": "Arms", "focus": ["arms"], "target_muscles": ["arms"]},
                {"day": 6, "name": "Core", "focus": ["core"], "target_muscles": ["core"]},
                {"day": 7, "name": "Cardio & Recovery", "focus": ["cardio"], "target_muscles": ["cardio"]}
            ]

    def _select_exercises_for_day(self, day_info, exercises_by_muscle, used_exercise_ids, fitness_level):
        """Select exercises for a specific day avoiding repetition"""
        day_exercises = []
        target_muscles = day_info["target_muscles"]
        exercises_per_muscle = 2 if len(target_muscles) > 2 else 3

        for muscle in target_muscles:
            muscle_exercises = exercises_by_muscle.get(muscle, [])

            # Filter out already used exercises
            available_exercises = [ex for ex in muscle_exercises if ex["id"] not in used_exercise_ids]

            # Select exercises for this muscle group
            selected_count = 0
            for exercise in available_exercises:
                if selected_count >= exercises_per_muscle:
                    break

                day_exercises.append({
                    "exercise_id": exercise["id"],
                    "name": exercise["name"],
                    "description": exercise["description"],
                    "muscle_groups": exercise["muscle_groups"],
                    "equipment": exercise["equipment"],
                    "sets": 3 if fitness_level == "beginner" else 4,
                    "reps": 12 if fitness_level == "beginner" else 10,
                    "duration_minutes": exercise["duration_minutes"],
                    "calories_per_minute": exercise["calories_per_minute"],
                    "rest_seconds": 60 if fitness_level == "beginner" else 45
                })

                used_exercise_ids.add(exercise["id"])
                selected_count += 1

        # Limit to 6-8 exercises per day
        max_exercises = 6 if fitness_level == "beginner" else 8
        return day_exercises[:max_exercises]

    def _calculate_workout_duration(self, exercises):
        """Calculate estimated workout duration"""
        total_minutes = 0
        for exercise in exercises:
            sets = exercise.get("sets", 3)
            duration_per_set = exercise.get("duration_minutes", 3)
            rest_time = exercise.get("rest_seconds", 60) / 60  # Convert to minutes
            total_minutes += (sets * duration_per_set) + (sets * rest_time)

        return int(total_minutes)

    def _get_fallback_workout_plan(self, user_profile):
        """Return a basic fallback workout plan"""
        return {
            "plan_name": "Basic 4-Day Workout Plan",
            "description": "A simple workout plan to get you started",
            "duration_weeks": 4,
            "plan_type": "4_day_split",
            "fitness_level": user_profile.get('fitness_level', 'beginner'),
            "workout_days_per_week": 4,
            "workouts": [
                {
                    "day": 1,
                    "name": "Upper Body",
                    "focus_areas": ["chest", "shoulders"],
                    "estimated_duration": 30,
                    "exercises": [
                        {
                            "exercise_id": 1,
                            "name": "Push-ups",
                            "description": "Classic bodyweight exercise",
                            "muscle_groups": ["chest", "shoulders"],
                            "equipment": ["bodyweight"],
                            "sets": 3,
                            "reps": 12,
                            "duration_minutes": 5,
                            "calories_per_minute": 6.0,
                            "rest_seconds": 60
                        }
                    ]
                }
            ]
        }

    # RBAC API Handlers
    def handle_admin_users_get(self):
        """Handle GET /api/admin/users - Get all users with roles"""
        try:
            # Check if user has admin permissions
            current_user = self.get_current_user_from_auth()
            if not self._has_permission(current_user, 'admin_access'):
                self.send_error(403, "Forbidden: Admin access required")
                return

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Get all users with their roles
            cursor.execute('''
                SELECT DISTINCT u.username, u.email, u.full_name, u.created_at,
                       GROUP_CONCAT(ur.role_name) as roles,
                       GROUP_CONCAT(CASE WHEN ur.is_default = 1 THEN ur.role_name END) as default_role
                FROM users u
                LEFT JOIN user_roles ur ON u.username = ur.username
                GROUP BY u.username, u.email, u.full_name, u.created_at
                ORDER BY u.created_at DESC
            ''')

            users = []
            for row in cursor.fetchall():
                users.append({
                    'username': row[0],
                    'email': row[1],
                    'full_name': row[2],
                    'created_at': row[3],
                    'roles': row[4].split(',') if row[4] else [],
                    'default_role': row[5] if row[5] else 'member'
                })

            conn.close()

            response = {
                'users': users,
                'total_count': len(users),
                'status': 'success'
            }

            self.send_response(200)
            self.send_cors_headers()
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Error fetching admin users: {e}")
            self.send_error(500, f"Internal Server Error: {e}")

    def _has_permission(self, username, permission):
        """Check if user has specific permission"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # Check if user has app_admin role (has all permissions)
            cursor.execute('''
                SELECT COUNT(*) FROM user_roles
                WHERE username = ? AND role_name = 'app_admin'
            ''', (username,))

            if cursor.fetchone()[0] > 0:
                conn.close()
                return True

            # For now, only app_admin has admin_access
            # In future, can implement more granular permissions
            conn.close()
            return False

        except Exception as e:
            print(f"❌ Error checking permissions: {e}")
            return False

    def handle_admin_roles_get(self):
        """Handle GET /api/admin/roles - Get all available roles"""
        try:
            current_user = self.get_current_user_from_auth()
            if not self._has_permission(current_user, 'admin_access'):
                self.send_error(403, "Forbidden: Admin access required")
                return

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT role_name, display_name, description, permissions, is_active
                FROM roles WHERE is_active = 1
                ORDER BY role_name
            ''')

            roles = []
            for row in cursor.fetchall():
                roles.append({
                    'role_name': row[0],
                    'display_name': row[1],
                    'description': row[2],
                    'permissions': json.loads(row[3]) if row[3] else [],
                    'is_active': bool(row[4])
                })

            conn.close()

            response = {
                'roles': roles,
                'status': 'success'
            }

            self.send_response(200)
            self.send_cors_headers()
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Error fetching roles: {e}")
            self.send_error(500, f"Internal Server Error: {e}")

    def handle_user_roles_get(self):
        """Handle GET /api/users/me/roles - Get current user's roles"""
        try:
            current_user = self.get_current_user_from_auth()

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT ur.role_name, r.display_name, ur.is_default
                FROM user_roles ur
                JOIN roles r ON ur.role_name = r.role_name
                WHERE ur.username = ? AND r.is_active = 1
                ORDER BY ur.is_default DESC, ur.role_name
            ''', (current_user,))

            roles = []
            for row in cursor.fetchall():
                roles.append({
                    'role_name': row[0],
                    'display_name': row[1],
                    'is_default': bool(row[2])
                })

            conn.close()

            response = {
                'roles': roles,
                'current_user': current_user,
                'status': 'success'
            }

            self.send_response(200)
            self.send_cors_headers()
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Error fetching user roles: {e}")
            self.send_error(500, f"Internal Server Error: {e}")

def run_sqlite_server(port=8080):
    """Run the SQLite server"""
    if not init_sqlite_database():
        print("❌ Failed to initialize SQLite database")
        return

    # Bind to all interfaces (0.0.0.0) to allow Android emulator access
    server_address = ('0.0.0.0', port)
    httpd = HTTPServer(server_address, WibeFitSQLiteHandler)

    print(f"🚀 WibeFit v1.0 SQLite Backend Server starting...")
    print(f"📍 Server running at:")
    print(f"   - Local: http://localhost:{port}")
    print(f"   - Network: http://0.0.0.0:{port}")
    print(f"   - Android Emulator: http://********:{port}")
    print(f"   - Health: http://localhost:{port}/health")
    print(f"💾 Database: SQLite (wibefit.db)")
    print(f"")
    print(f"🔑 Demo Login Credentials:")
    print(f"   - Username: demo")
    print(f"   - Password: demo123")
    print(f"")
    print(f"🤖 Android Emulator Support: ENABLED")
    print(f"📱 iOS Simulator Support: ENABLED")
    print(f"")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == '__main__':
    run_sqlite_server()
