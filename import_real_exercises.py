#!/usr/bin/env python3
"""
Import all 1062+ real exercises from extracted_exercises.txt into SQLite database
"""

import sqlite3
import json
import re
import hashlib
import secrets

# Database file
DB_FILE = 'wibefit.db'

def create_slug(name, cursor, counter=0):
    """Create URL-friendly slug from exercise name"""
    base_slug = name.lower()
    base_slug = re.sub(r'[^a-z0-9\s-]', '', base_slug)  # Remove special chars except spaces and hyphens
    base_slug = re.sub(r'\s+', '-', base_slug)  # Replace spaces with hyphens
    base_slug = re.sub(r'-+', '-', base_slug)   # Replace multiple hyphens with single
    base_slug = base_slug.strip('-')            # Remove leading/trailing hyphens

    # Handle duplicates by adding counter
    if counter > 0:
        slug = f"{base_slug}-{counter}"
    else:
        slug = base_slug

    # Check if slug exists
    cursor.execute("SELECT COUNT(*) FROM exercises WHERE slug = ?", (slug,))
    if cursor.fetchone()[0] > 0:
        return create_slug(name, cursor, counter + 1)

    return slug

def categorize_exercise(name):
    """Categorize exercise based on name patterns"""
    name_lower = name.lower()
    
    # Core/Abs exercises
    if any(word in name_lower for word in ['ab', 'abs', 'crunch', 'sit-up', 'plank', 'twist', 'oblique', 'heel touch']):
        return {
            'category': 'core',
            'muscle_groups': ['core', 'abs'],
            'exercise_type': 'strength'
        }
    
    # Back exercises
    elif any(word in name_lower for word in ['back', 'row', 'pull', 'lat', 'deadlift', 'hyperextension']):
        return {
            'category': 'back',
            'muscle_groups': ['back'],
            'exercise_type': 'strength'
        }
    
    # Chest exercises
    elif any(word in name_lower for word in ['chest', 'push', 'press', 'fly', 'pec']):
        return {
            'category': 'chest',
            'muscle_groups': ['chest'],
            'exercise_type': 'strength'
        }
    
    # Leg exercises
    elif any(word in name_lower for word in ['leg', 'squat', 'lunge', 'calf', 'thigh', 'quad', 'hamstring']):
        return {
            'category': 'legs',
            'muscle_groups': ['legs'],
            'exercise_type': 'strength'
        }
    
    # Shoulder exercises
    elif any(word in name_lower for word in ['shoulder', 'deltoid', 'raise', 'shrug']):
        return {
            'category': 'shoulders',
            'muscle_groups': ['shoulders'],
            'exercise_type': 'strength'
        }
    
    # Arm exercises
    elif any(word in name_lower for word in ['arm', 'bicep', 'tricep', 'curl', 'extension']):
        return {
            'category': 'arms',
            'muscle_groups': ['arms'],
            'exercise_type': 'strength'
        }
    
    # Cardio exercises
    elif any(word in name_lower for word in ['bike', 'run', 'jump', 'cardio', 'burpee']):
        return {
            'category': 'cardio',
            'muscle_groups': ['full_body'],
            'exercise_type': 'cardio'
        }
    
    # Default to strength
    else:
        return {
            'category': 'strength',
            'muscle_groups': ['full_body'],
            'exercise_type': 'strength'
        }

def determine_difficulty(name):
    """Determine difficulty based on exercise name"""
    name_lower = name.lower()
    
    if any(word in name_lower for word in ['advanced', 'expert', 'extreme', 'heavy']):
        return 'advanced'
    elif any(word in name_lower for word in ['intermediate', 'medium']):
        return 'intermediate'
    else:
        return 'beginner'

def determine_equipment(name):
    """Determine required equipment based on exercise name"""
    name_lower = name.lower()
    equipment = []
    
    if any(word in name_lower for word in ['dumbbell', 'db']):
        equipment.append('dumbbells')
    elif any(word in name_lower for word in ['barbell', 'bb']):
        equipment.append('barbells')
    elif any(word in name_lower for word in ['machine', 'cable']):
        equipment.append('gym_equipment')
    elif any(word in name_lower for word in ['ball', 'swiss']):
        equipment.append('exercise_ball')
    elif any(word in name_lower for word in ['band', 'resistance']):
        equipment.append('resistance_bands')
    elif any(word in name_lower for word in ['wheel', 'ab wheel']):
        equipment.append('ab_wheel')
    else:
        equipment.append('bodyweight')
    
    return equipment

def import_exercises():
    """Import all exercises from extracted_exercises.txt"""
    try:
        # Read the exercises file
        with open('../extracted_exercises.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Connect to database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Clear existing exercises (keep only the first 10 as backup)
        cursor.execute("DELETE FROM exercises WHERE id > 10")
        
        exercises_added = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse line format: "number. Exercise Name"
            match = re.match(r'^\d+\.\s*(.+)$', line)
            if not match:
                continue
            
            exercise_name = match.group(1).strip()
            if not exercise_name:
                continue
            
            # Skip duplicates
            cursor.execute("SELECT COUNT(*) FROM exercises WHERE name = ?", (exercise_name,))
            if cursor.fetchone()[0] > 0:
                continue
            
            # Create exercise data
            slug = create_slug(exercise_name, cursor)
            categorization = categorize_exercise(exercise_name)
            difficulty = determine_difficulty(exercise_name)
            equipment = determine_equipment(exercise_name)
            
            # Calculate duration and calories based on category
            if categorization['exercise_type'] == 'cardio':
                duration = 3
                calories_per_minute = 8.0
            else:
                duration = 5
                calories_per_minute = 6.0
            
            # Insert exercise
            cursor.execute('''
                INSERT INTO exercises (
                    name, slug, description, muscle_groups, equipment, difficulty,
                    exercise_type, category, duration_minutes, calories_per_minute,
                    instructions, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                exercise_name,
                slug,
                f"Effective {categorization['category']} exercise targeting {', '.join(categorization['muscle_groups'])}",
                json.dumps(categorization['muscle_groups']),
                json.dumps(equipment),
                difficulty,
                categorization['exercise_type'],
                categorization['category'],
                duration,
                calories_per_minute,
                f"Perform {exercise_name} with proper form and controlled movement",
                1
            ))
            
            exercises_added += 1
            
            if exercises_added % 100 == 0:
                print(f"✅ Added {exercises_added} exercises...")
        
        conn.commit()
        conn.close()
        
        print(f"🎉 Successfully imported {exercises_added} exercises!")
        print(f"📊 Total exercises in database: {exercises_added + 10}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing exercises: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting exercise import...")
    if import_exercises():
        print("✅ Exercise import completed successfully!")
    else:
        print("❌ Exercise import failed!")
