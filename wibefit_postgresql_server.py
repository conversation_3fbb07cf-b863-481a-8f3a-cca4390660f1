#!/usr/bin/env python3
"""
WibeFit v1.0 PostgreSQL Backend Server
Backend server that connects to your local PostgreSQL database
"""

import json
import os
import psycopg2
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Password hashing utilities
def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
    return f"{salt}:{password_hash}"

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        salt, stored_hash = hashed_password.split(':')
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return password_hash == stored_hash
    except ValueError:
        return False

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        salt, stored_hash = hashed_password.split(':')
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return password_hash == stored_hash
    except ValueError:
        return False

# PostgreSQL connection settings
DB_CONFIG = {
    'host': 'localhost',
    'database': 'wibefit_db',
    'user': 'sanju',  # Change this to your PostgreSQL username
    'password': '0810',  # Change this to your PostgreSQL password
    'port': 5432
}

def init_postgresql_database():
    """Initialize PostgreSQL database with required tables"""
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if tables exist - only create if they don't exist
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'users'
            );
        """)
        tables_exist = cursor.fetchone()[0]

        if tables_exist:
            print("✅ Database tables already exist - preserving existing data")
        else:
            print("🔧 Creating database tables for the first time")

        # Users table with username as primary key
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                username TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                password_hash TEXT,
                role TEXT DEFAULT 'user',
                bio TEXT,
                profile_picture TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_login_at TIMESTAMP,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        ''')
        
        # User profiles table with username as primary key
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_profiles (
                username TEXT PRIMARY KEY REFERENCES users(username) ON DELETE CASCADE,
                age INTEGER,
                phone TEXT,
                gender TEXT,
                height REAL,
                current_weight REAL,
                fitness_level TEXT,
                primary_goals JSONB DEFAULT '[]'::jsonb,
                equipment_access JSONB DEFAULT '[]'::jsonb,
                preferred_workout_days JSONB DEFAULT '[]'::jsonb,
                activity_level TEXT,
                preferred_workout_duration INTEGER,
                profile_setup_completed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Workout sessions table with username reference
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workout_sessions (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                workout_plan_id TEXT,
                workout_name TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration_minutes INTEGER,
                calories_burned INTEGER,
                body_weight REAL,
                status TEXT DEFAULT 'completed',
                exercises JSONB NOT NULL DEFAULT '[]'::jsonb,
                metadata JSONB DEFAULT '{}'::jsonb,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Workout logs table (alias for workout_sessions for compatibility)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workout_logs (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration_minutes INTEGER,
                calories_burned INTEGER,
                exercises JSONB NOT NULL DEFAULT '[]'::jsonb,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Body weight tracking table with username reference
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS body_weights (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                weight REAL NOT NULL,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )
        ''')
        
        # Request logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS request_logs (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                method TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                data JSONB,
                response_status INTEGER,
                created_at TIMESTAMP NOT NULL
            )
        ''')

        # Community posts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS community_posts (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                content TEXT NOT NULL,
                post_type TEXT DEFAULT 'general',
                likes_count INTEGER DEFAULT 0,
                comments_count INTEGER DEFAULT 0,
                media_urls TEXT[],
                is_public BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Challenges table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS challenges (
                id SERIAL PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                challenge_type TEXT DEFAULT 'fitness',
                start_date TIMESTAMP,
                end_date TIMESTAMP,
                participants_count INTEGER DEFAULT 0,
                reward_points INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT true,
                created_by TEXT REFERENCES users(username) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Friendships table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS friendships (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                friend_username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(username, friend_username)
            )
        ''')

        # User achievements table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_achievements (
                id SERIAL PRIMARY KEY,
                username TEXT NOT NULL REFERENCES users(username) ON DELETE CASCADE,
                achievement_type TEXT NOT NULL,
                achievement_name TEXT NOT NULL,
                description TEXT,
                points_earned INTEGER DEFAULT 0,
                earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Only insert demo data if tables are empty (first time setup)
        if not tables_exist:
            print("🔧 Inserting demo data for first time setup...")

            # Insert demo users with usernames and passwords
            demo_password_hash = hash_password('demo123')  # Default password for all demo users
            cursor.execute('''
                INSERT INTO users (username, email, full_name, password_hash, role, created_at, last_login_at, metadata, bio) VALUES
                ('demo', '<EMAIL>', 'Demo User', %s, 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Demo user for testing'),
                ('demouser', '<EMAIL>', 'Demo User Local', %s, 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Local demo user'),
                ('admin', '<EMAIL>', 'Admin User', %s, 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Administrator'),
                ('fitness_guru', '<EMAIL>', 'Alex Johnson', %s, 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Certified personal trainer & nutrition coach'),
                ('yoga_master', '<EMAIL>', 'Sarah Chen', %s, 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Yoga instructor & mindfulness coach'),
                ('cardio_king', '<EMAIL>', 'Mike Rodriguez', %s, 'user', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{}', 'Marathon runner & endurance coach')
            ''', (demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash, demo_password_hash))

            # Insert sample community posts
            cursor.execute('''
            INSERT INTO community_posts (username, content, post_type, likes_count, comments_count, created_at) VALUES
            ('fitness_guru', 'Just completed an amazing 45-minute HIIT workout! 💪 Feeling stronger every day. Who else is crushing their fitness goals today?', 'workout', 24, 8, CURRENT_TIMESTAMP - INTERVAL '2 hours'),
            ('yoga_master', 'Morning yoga session complete! 🧘‍♀️ Starting the day with mindfulness and movement. Remember, progress over perfection!', 'general', 18, 5, CURRENT_TIMESTAMP - INTERVAL '4 hours'),
            ('cardio_king', 'New personal record! 🏃‍♂️ Just ran 10K in under 45 minutes. Training for the marathon is paying off!', 'achievement', 32, 12, CURRENT_TIMESTAMP - INTERVAL '6 hours')
            ON CONFLICT DO NOTHING
        ''')

            # Insert sample challenges
            cursor.execute('''
            INSERT INTO challenges (title, description, challenge_type, start_date, end_date, participants_count, reward_points, created_by) VALUES
            ('30-Day Fitness Challenge', 'Complete 30 workouts in 30 days', 'fitness', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', 156, 500, 'admin'),
            ('Weekly Step Challenge', 'Walk 10,000 steps every day this week', 'cardio', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '7 days', 89, 200, 'admin'),
            ('Strength Training Month', 'Focus on strength training for the entire month', 'strength', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', 67, 400, 'admin')
            ON CONFLICT DO NOTHING
        ''')

            # Insert sample workout sessions for leaderboard
            cursor.execute('''
            INSERT INTO workout_logs (username, start_time, end_time, duration_minutes, calories_burned, exercises) VALUES
            ('fitness_guru', CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day' + INTERVAL '45 minutes', 45, 380, '[{"name": "HIIT Workout", "duration": 45}]'::jsonb),
            ('yoga_master', CURRENT_TIMESTAMP - INTERVAL '2 days', CURRENT_TIMESTAMP - INTERVAL '2 days' + INTERVAL '60 minutes', 60, 250, '[{"name": "Yoga Flow", "duration": 60}]'::jsonb),
            ('cardio_king', CURRENT_TIMESTAMP - INTERVAL '3 days', CURRENT_TIMESTAMP - INTERVAL '3 days' + INTERVAL '50 minutes', 50, 450, '[{"name": "Running", "duration": 50}]'::jsonb)
            ON CONFLICT DO NOTHING
        ''')

            # Insert demo profile for main demo user
            cursor.execute('''
            INSERT INTO user_profiles (
                username, age, phone, gender, height, current_weight, fitness_level,
                primary_goals, equipment_access, preferred_workout_days,
                activity_level, preferred_workout_duration, profile_setup_completed,
                created_at, updated_at
            ) VALUES (
                'demo', 25, '+1234567890', 'male', 175.0, 70.0, 'intermediate',
                '["Weight Loss", "Muscle Gain"]'::jsonb,
                '["Full Gym Access"]'::jsonb,
                '["Monday", "Wednesday", "Friday"]'::jsonb,
                'moderately_active', 45, true,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            ) ON CONFLICT (username) DO NOTHING
        ''')

            # Insert sample workout session
            cursor.execute('''
            INSERT INTO workout_sessions (
                username, workout_name, duration_minutes, calories_burned,
                exercises, status, created_at
            ) VALUES (
                'demo', 'Morning Workout', 30, 250,
                '[{"name": "Push-ups", "sets": 3, "reps": 15}, {"name": "Squats", "sets": 3, "reps": 20}]'::jsonb,
                'completed', CURRENT_TIMESTAMP
            )
        ''')

            # Insert sample body weight record
            cursor.execute('''
                INSERT INTO body_weights (username, weight, recorded_at, notes) VALUES
                ('demo', 70.0, CURRENT_TIMESTAMP, 'Starting weight')
            ''')

            print("✅ Demo data inserted successfully")
        else:
            print("✅ Preserving existing user data - no demo data insertion")

        conn.commit()
        conn.close()
        print("✅ PostgreSQL database initialized successfully")
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL connection error: {e}")
        print("Please ensure PostgreSQL is running and credentials are correct")
        return False
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def log_request(method: str, endpoint: str, data: Any = None, status: int = 200):
    """Log request to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO request_logs (timestamp, method, endpoint, data, response_status, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        ''', (
            datetime.now(),
            method,
            endpoint,
            json.dumps(data) if data else None,
            status,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
        print(f"📝 LOGGED: {method} {endpoint} - Status: {status}")
    except Exception as e:
        print(f"❌ Error logging request: {e}")

class WibeFitPostgreSQLHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.handle_root()
        elif self.path == '/health':
            self.handle_health()
        elif self.path == '/api/auth/profile' or self.path.startswith('/api/auth/profile?'):
            self.handle_profile_get()
        elif self.path == '/api/users/me/profile' or self.path.startswith('/api/users/me/profile?'):
            self.handle_profile_get()
        elif self.path == '/api/exercises':
            self.handle_exercises_get()
        elif self.path.startswith('/api/debug'):
            self.handle_debug()
        elif self.path.startswith('/api/community/posts'):
            self.handle_community_posts_get()
        elif self.path.startswith('/api/community/challenges'):
            self.handle_challenges_get()
        elif self.path.startswith('/api/community/leaderboard'):
            self.handle_leaderboard_get()
        elif self.path.startswith('/api/community/friends'):
            self.handle_friends_get()
        elif self.path.startswith('/api/community/users') and '/discover' in self.path:
            self.handle_discover_users_get()
        elif self.path.startswith('/api/community/users') and '/search' in self.path:
            self.handle_search_users_get()
        elif self.path == '/api/users/me':
            self.handle_users_me_get()
        elif self.path == '/api/users/me/stats':
            self.handle_users_me_stats_get()
        elif self.path == '/api/users/me/profile':
            self.handle_users_me_profile_get()
        elif self.path.startswith('/api/community/users/'):
            self.handle_community_users_get()
        elif self.path.startswith('/api/workouts/exercises/favorites'):
            self.handle_exercises_favorites_get()
        elif self.path.startswith('/api/workouts/exercises'):
            self.handle_workouts_exercises_get()
        elif self.path.startswith('/api/exercises'):
            self.handle_exercises_get()
        else:
            self.send_error(404, "Not Found")

    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            data = {}

        if self.path == '/api/auth/register':
            self.handle_register(data)
        elif self.path == '/api/auth/login':
            self.handle_login(data)
        elif self.path == '/api/auth/login-username':
            self.handle_login_username(data)
        elif self.path == '/api/auth/register-username':
            self.handle_register_username(data)
        elif self.path == '/api/auth/profile':
            self.handle_profile_update(data)
        elif self.path == '/api/users/me/profile':
            self.handle_profile_update(data)
        elif self.path == '/api/workout/session':
            self.handle_workout_session(data)
        elif self.path == '/api/body-weight':
            self.handle_body_weight(data)
        elif self.path == '/api/ai/generate-workout-plan':
            self.handle_ai_workout_generation(data)
        elif self.path == '/api/ai/select-exercises':
            self.handle_ai_exercise_selection(data)
        else:
            self.send_error(404, "Not Found")

    def do_PUT(self):
        """Handle PUT requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            data = {}

        if self.path == '/api/users/me/profile':
            self.handle_profile_update(data)
        elif self.path == '/api/auth/profile':
            self.handle_profile_update(data)
        else:
            self.send_error(404, "Not Found")

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    def handle_root(self):
        """Handle root endpoint"""
        log_request('GET', '/')
        
        response = {
            "message": "WibeFit v1.0 PostgreSQL Backend Server",
            "version": "1.0.0",
            "database": "PostgreSQL",
            "timestamp": datetime.now().isoformat(),
            "status": "running"
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_health(self):
        """Handle health check"""
        log_request('GET', '/health')
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM workout_sessions")
            workout_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM request_logs")
            log_count = cursor.fetchone()[0]
            
            conn.close()
            
            response = {
                "status": "healthy",
                "database": "PostgreSQL",
                "timestamp": datetime.now().isoformat(),
                "stats": {
                    "users": user_count,
                    "workouts": workout_count,
                    "logs": log_count
                }
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            response = {
                "status": "error",
                "message": f"Database connection failed: {e}"
            }
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

    def handle_register(self, data):
        """Handle user registration with username"""
        log_request('POST', '/api/auth/register', data)

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Generate username from email or use provided username
            email = data.get('email', '')
            username = data.get('username')

            if not username:
                # Generate username from email
                username = email.split('@')[0] if email else f"user_{int(datetime.now().timestamp())}"

            # Check if username already exists
            cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                # Add number suffix if username exists
                base_username = username
                counter = 1
                while True:
                    username = f"{base_username}{counter}"
                    cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
                    if not cursor.fetchone():
                        break
                    counter += 1

            # Hash the password if provided
            password = data.get('password')
            password_hash = hash_password(password) if password else None

            cursor.execute('''
                INSERT INTO users (username, email, full_name, password_hash, role, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            ''', (
                username,
                email,
                data.get('full_name', ''),
                password_hash,
                'user',
                datetime.now()
            ))

            conn.commit()
            conn.close()

            response = {
                "message": "User registered successfully",
                "user": {
                    "username": username,
                    "email": email,
                    "full_name": data.get('full_name')
                }
            }

            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

            print(f"✅ USER REGISTERED: {email} -> USERNAME: {username}")
            
        except Exception as e:
            print(f"❌ Registration error: {e}")
            self.send_error(500, str(e))

    def handle_login(self, data):
        """Handle user login with username or email"""
        log_request('POST', '/api/auth/login', data)

        try:
            # Support both email and username login
            email = data.get('email')
            username = data.get('username')
            password = data.get('password')

            if not (email or username):
                self.send_error(400, "Email or username required")
                return

            if not password:
                self.send_error(400, "Password required")
                return

            # Query database for user including password hash
            conn = get_db_connection()
            cursor = conn.cursor()

            if email:
                # Login with email
                cursor.execute("SELECT username, email, full_name, role, password_hash FROM users WHERE email = %s", (email,))
            else:
                # Login with username
                cursor.execute("SELECT username, email, full_name, role, password_hash FROM users WHERE username = %s", (username,))

            user_row = cursor.fetchone()
            cursor.close()
            conn.close()

            if not user_row:
                self.send_error(401, "Invalid credentials")
                return

            # Verify password
            stored_password_hash = user_row[4]  # password_hash is the 5th column
            if stored_password_hash and not verify_password(password, stored_password_hash):
                self.send_error(401, "Invalid credentials")
                return
            elif not stored_password_hash:
                # User has no password set (legacy user), allow login but should set password
                print(f"⚠️ User {user_row[0]} has no password set")
                pass

            # Create response with username as primary identifier
            response = {
                "access_token": f"token_{int(datetime.now().timestamp())}",
                "token_type": "bearer",
                "user": {
                    "username": user_row[0],
                    "email": user_row[1],
                    "full_name": user_row[2],
                    "role": user_row[3]
                }
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

            print(f"✅ USER LOGGED IN: {user_row[0]} ({user_row[1]})")

        except Exception as e:
            print(f"❌ Login error: {e}")
            self.send_error(500, str(e))

    def handle_profile_update(self, data):
        """Handle profile update with username"""
        log_request('POST', '/api/auth/profile', data)

        print(f"🔍 PROFILE UPDATE REQUEST:")
        print(f"📊 Raw data received: {json.dumps(data, indent=2)}")

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Get username from data
            username = data.get('username')
            if not username:
                # Try to get from user_id field (fallback)
                username = data.get('user_id')

            if not username:
                # For /api/users/me/profile endpoint, use the most recently logged-in user
                if self.path == '/api/users/me/profile':
                    print("⚠️ No username provided for /api/users/me/profile, using most recently logged-in user...")
                    cursor.execute("""
                        SELECT up.username FROM user_profiles up
                        JOIN users u ON up.username = u.username
                        WHERE u.is_active = true
                        ORDER BY u.last_login_at DESC NULLS LAST, u.username = 'demo' DESC
                        LIMIT 1
                    """)
                    recent_user = cursor.fetchone()
                    if recent_user:
                        username = recent_user[0]
                        print(f"✅ Using most recently logged-in user: {username}")
                    else:
                        print("❌ No active users with profiles found")
                        self.send_response(400)
                        self.send_header('Content-Type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({"error": "No active user found for profile update"}).encode())
                        return
                else:
                    # Fallback: Use the most recently logged-in user for profile update
                    print("⚠️ No username provided, using most recently logged-in user for profile update...")
                    cursor.execute("""
                        SELECT u.username FROM users u
                        WHERE u.is_active = true
                        ORDER BY u.last_login_at DESC NULLS LAST, u.username = 'demo' DESC
                        LIMIT 1
                    """)
                    recent_user = cursor.fetchone()
                    if recent_user:
                        username = recent_user[0]
                        print(f"✅ Using most recently logged-in user: {username}")
                    else:
                        print("❌ No active users found")
                        self.send_response(400)
                        self.send_header('Content-Type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({"error": "No active user found for profile update"}).encode())
                        return

            print(f"👤 Using username: {username}")

            # Update user's last activity (for tracking current user)
            cursor.execute("""
                UPDATE users SET last_login_at = %s WHERE username = %s
            """, (datetime.now(), username))

            # Check if profile exists for this user
            cursor.execute('SELECT username FROM user_profiles WHERE username = %s', (username,))
            existing_profile = cursor.fetchone()

            if existing_profile:
                # Update existing profile
                cursor.execute('''
                    UPDATE user_profiles SET
                        age = %s,
                        height = %s,
                        current_weight = %s,
                        fitness_level = %s,
                        primary_goals = %s,
                        equipment_access = %s,
                        preferred_workout_days = %s,
                        gender = %s,
                        profile_setup_completed = %s,
                        updated_at = %s,
                        phone = %s,
                        activity_level = %s,
                        preferred_workout_duration = %s
                    WHERE username = %s
                ''', (
                    data.get('age'),
                    data.get('height'),
                    data.get('current_weight'),
                    data.get('fitness_level'),
                    json.dumps(data.get('primary_goals', [])),
                    json.dumps(data.get('equipment_access', [])),
                    json.dumps(data.get('preferred_workout_days', [])),
                    data.get('gender'),
                    True,
                    datetime.now(),
                    data.get('phone'),
                    data.get('activity_level'),
                    data.get('preferred_workout_duration', 30),
                    username
                ))
                print(f"✅ PROFILE UPDATED: User {username}")
            else:
                # Insert new profile
                cursor.execute('''
                    INSERT INTO user_profiles
                    (username, age, height, current_weight, fitness_level, primary_goals,
                     equipment_access, preferred_workout_days, gender, profile_setup_completed,
                     created_at, updated_at, phone,
                     activity_level, preferred_workout_duration)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    username,
                    data.get('age'),
                    data.get('height'),
                    data.get('current_weight'),
                    data.get('fitness_level'),
                    json.dumps(data.get('primary_goals', [])),
                    json.dumps(data.get('equipment_access', [])),
                    json.dumps(data.get('preferred_workout_days', [])),
                    data.get('gender'),
                    True,
                    datetime.now(),
                    datetime.now(),
                    data.get('phone'),
                    data.get('activity_level'),
                    data.get('preferred_workout_duration', 30)
                ))
                print(f"✅ PROFILE CREATED: User {username}")

            # Log the profile data
            print(f"📊 Profile Data: {json.dumps(data, indent=2)}")
            
            conn.commit()
            conn.close()
            
            response = {"message": "Profile updated successfully"}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ PROFILE UPDATED: User {username}")
            print(f"📊 Profile Data: {json.dumps(data, indent=2)}")
            
        except Exception as e:
            print(f"❌ Profile update error: {e}")
            self.send_error(500, str(e))

    def handle_profile_get(self):
        """Handle profile GET request"""
        log_request('GET', '/api/auth/profile')

        try:
            # Parse query parameters to get specific username
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            requested_username = query_params.get('username', [None])[0]

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            if requested_username:
                # Get profile for specific user
                print(f"🔍 Getting profile for specific user: {requested_username}")
                cursor.execute("""
                    SELECT up.username, up.age, up.height, up.current_weight, up.fitness_level,
                           up.primary_goals, up.equipment_access, up.preferred_workout_days,
                           up.gender, up.profile_setup_completed, up.created_at,
                           u.email, up.phone, up.activity_level, up.preferred_workout_duration
                    FROM user_profiles up
                    JOIN users u ON up.username = u.username
                    WHERE up.username = %s AND u.is_active = true
                """, (requested_username,))
            else:
                # For /api/users/me/profile, try to get the most recently logged-in user's profile
                if self.path.startswith('/api/users/me/profile'):
                    print("🔍 No specific user requested for /api/users/me/profile, getting most recently logged-in user")
                    cursor.execute("""
                        SELECT up.username, up.age, up.height, up.current_weight, up.fitness_level,
                               up.primary_goals, up.equipment_access, up.preferred_workout_days,
                               up.gender, up.profile_setup_completed, up.created_at,
                               u.email, up.phone, up.activity_level, up.preferred_workout_duration
                        FROM user_profiles up
                        JOIN users u ON up.username = u.username
                        WHERE u.is_active = true
                        ORDER BY u.last_login_at DESC NULLS LAST, u.username = 'demo' DESC
                        LIMIT 1
                    """)
                else:
                    # For /api/auth/profile, get the most recently logged-in user's profile
                    print("🔍 No specific user requested, getting most recently logged-in user's profile")
                    cursor.execute("""
                        SELECT up.username, up.age, up.height, up.current_weight, up.fitness_level,
                               up.primary_goals, up.equipment_access, up.preferred_workout_days,
                               up.gender, up.profile_setup_completed, up.created_at,
                               u.email, up.phone, up.activity_level, up.preferred_workout_duration
                        FROM user_profiles up
                        JOIN users u ON up.username = u.username
                        WHERE u.is_active = true
                        ORDER BY u.last_login_at DESC NULLS LAST, u.username = 'demo' DESC
                        LIMIT 1
                    """)

            profile_row = cursor.fetchone()

            if profile_row:
                response = {
                    "user_id": profile_row[0],  # username
                    "username": profile_row[0],  # Include username field for app compatibility
                    "age": profile_row[1],
                    "height": profile_row[2],
                    "current_weight": profile_row[3],
                    "fitness_level": profile_row[4],
                    "primary_goals": profile_row[5],
                    "equipment_access": profile_row[6],
                    "preferred_workout_days": profile_row[7],
                    "gender": profile_row[8],
                    "profile_setup_completed": profile_row[9],
                    "profile_setup_date": str(profile_row[10]) if profile_row[10] else None,  # created_at
                    "email": profile_row[11],
                    "phone": profile_row[12],
                    "activity_level": profile_row[13],
                    "preferred_workout_duration": profile_row[14]
                }
            else:
                # Return empty profile if none found
                response = {
                    "user_id": None,
                    "username": None,  # Include username field for app compatibility
                    "age": None,
                    "height": None,
                    "current_weight": None,
                    "fitness_level": None,
                    "primary_goals": [],
                    "equipment_access": [],
                    "preferred_workout_days": [],
                    "gender": None,
                    "profile_setup_completed": False,
                    "profile_setup_date": None,
                    "email": None,
                    "phone": None,
                    "date_of_birth": None,
                    "activity_level": None,
                    "preferred_workout_duration": None
                }

            conn.close()

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

            print(f"✅ Profile retrieved successfully")

        except Exception as e:
            print(f"❌ Profile get error: {e}")
            self.send_error(500, str(e))

    def handle_exercises_get(self):
        """Handle exercises GET request"""
        log_request('GET', '/api/exercises')

        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get all exercises
            cursor.execute("""
                SELECT id, name, category, muscle_groups, equipment_needed,
                       difficulty_level, instructions, video_url, image_url,
                       duration_minutes, calories_per_minute, created_at, updated_at
                FROM exercises
                ORDER BY name
            """)

            exercises = cursor.fetchall()
            conn.close()

            response = []
            for exercise in exercises:
                response.append({
                    "id": exercise[0],
                    "name": exercise[1],
                    "category": exercise[2],
                    "muscle_groups": exercise[3],
                    "equipment_needed": exercise[4],
                    "difficulty_level": exercise[5],
                    "instructions": exercise[6],
                    "video_url": exercise[7],
                    "image_url": exercise[8],
                    "duration_minutes": exercise[9],
                    "calories_per_minute": exercise[10],
                    "created_at": exercise[11].isoformat() if exercise[11] else None,
                    "updated_at": exercise[12].isoformat() if exercise[12] else None
                })

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

            print(f"✅ Exercises retrieved successfully: {len(response)} exercises")

        except Exception as e:
            print(f"❌ Exercises retrieval error: {e}")
            self.send_error(500, str(e))

    def handle_workout_session(self, data):
        """Handle workout session save"""
        log_request('POST', '/api/workout/session', data)
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            # Generate a unique session ID
            session_id = f"session_{int(datetime.now().timestamp())}"

            # Calculate start and end times if not provided
            now = datetime.now()
            duration_minutes = data.get('duration_minutes', 0)
            start_time = datetime.fromisoformat(data.get('start_time').replace('Z', '+00:00')) if data.get('start_time') else (now - timedelta(minutes=duration_minutes))
            end_time = datetime.fromisoformat(data.get('end_time').replace('Z', '+00:00')) if data.get('end_time') else now

            cursor.execute('''
                INSERT INTO workout_sessions
                (id, user_id, workout_plan_id, workout_name, start_time, end_time,
                 duration_minutes, calories_burned, body_weight, status, exercises, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                session_id,
                data.get('user_id'),
                data.get('workout_plan_id'),
                data.get('workout_name'),
                start_time,
                end_time,
                duration_minutes,
                data.get('calories_burned', 0),
                data.get('body_weight'),
                data.get('status', 'completed'),
                json.dumps(data.get('exercises', [])),
                now
            ))
            
            conn.commit()
            conn.close()
            
            response = {"message": "Workout session saved successfully"}
            
            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ WORKOUT SAVED: {data.get('workout_name')} - {data.get('duration_minutes')} min")
            
        except Exception as e:
            print(f"❌ Workout save error: {e}")
            self.send_error(500, str(e))

    def handle_debug(self):
        """Handle debug endpoint"""
        log_request('GET', '/api/debug')
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM workout_sessions")
            workout_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM request_logs")
            log_count = cursor.fetchone()[0]
            
            conn.close()
            
            response = {
                "database": "PostgreSQL",
                "users": user_count,
                "workouts": workout_count,
                "logs": log_count,
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            print(f"❌ Debug error: {e}")
            self.send_error(500, str(e))

    def handle_community_posts_get(self):
        """Handle GET /api/community/posts"""
        try:
            # Parse query parameters
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            limit = int(query_params.get('limit', [20])[0])
            offset = int(query_params.get('offset', [0])[0])

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get community posts with user information
            cursor.execute("""
                SELECT
                    p.id, p.username, u.full_name, p.content, p.post_type,
                    p.likes_count, p.comments_count, p.created_at, p.media_urls
                FROM community_posts p
                JOIN users u ON p.username = u.username
                WHERE p.is_public = true
                ORDER BY p.created_at DESC
                LIMIT %s OFFSET %s
            """, (limit, offset))

            posts = []
            for row in cursor.fetchall():
                posts.append({
                    'id': row[0],
                    'username': row[1],
                    'user_name': row[2],
                    'content': row[3],
                    'post_type': row[4],
                    'likes_count': row[5],
                    'comments_count': row[6],
                    'created_at': row[7].isoformat() if row[7] else None,
                    'media_urls': row[8] or [],
                    'user_liked': False  # TODO: Check if current user liked this post
                })

            conn.close()

            response = {'posts': posts}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Community posts error: {e}")
            self.send_error(500, "Failed to fetch community posts")

    def handle_challenges_get(self):
        """Handle GET /api/community/challenges"""
        try:
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            limit = int(query_params.get('limit', [10])[0])

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get active challenges
            cursor.execute("""
                SELECT id, title, description, challenge_type, start_date, end_date,
                       participants_count, reward_points, created_at
                FROM challenges
                WHERE is_active = true AND end_date > NOW()
                ORDER BY created_at DESC
                LIMIT %s
            """, (limit,))

            challenges = []
            for row in cursor.fetchall():
                challenges.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'challenge_type': row[3],
                    'start_date': row[4].isoformat() if row[4] else None,
                    'end_date': row[5].isoformat() if row[5] else None,
                    'participants_count': row[6],
                    'reward_points': row[7],
                    'created_at': row[8].isoformat() if row[8] else None,
                    'is_participating': False  # TODO: Check if user is participating
                })

            conn.close()

            response = {'challenges': challenges}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Challenges error: {e}")
            self.send_error(500, "Failed to fetch challenges")

    def handle_leaderboard_get(self):
        """Handle GET /api/community/leaderboard"""
        try:
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            limit = int(query_params.get('limit', [10])[0])

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Calculate leaderboard based on workout sessions and achievements
            cursor.execute("""
                SELECT
                    u.username, u.full_name,
                    COUNT(ws.id) as workouts_completed,
                    COALESCE(SUM(ws.calories_burned), 0) as total_calories,
                    COUNT(ua.id) as achievements_count,
                    (COUNT(ws.id) * 10 + COUNT(ua.id) * 50) as points
                FROM users u
                LEFT JOIN workout_sessions ws ON u.username = ws.username
                LEFT JOIN user_achievements ua ON u.username = ua.username
                WHERE u.is_active = true
                GROUP BY u.username, u.full_name
                ORDER BY points DESC
                LIMIT %s
            """, (limit,))

            leaderboard = []
            rank = 1
            for row in cursor.fetchall():
                leaderboard.append({
                    'rank': rank,
                    'username': row[0],
                    'full_name': row[1],
                    'workouts_completed': row[2],
                    'total_calories': row[3],
                    'achievements_count': row[4],
                    'points': row[5],
                    'streak_days': 0  # TODO: Calculate streak
                })
                rank += 1

            conn.close()

            response = {'leaderboard': leaderboard}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Leaderboard error: {e}")
            self.send_error(500, "Failed to fetch leaderboard")

    def handle_friends_get(self):
        """Handle GET /api/community/friends"""
        try:
            # TODO: Get current user from auth token
            current_username = 'testuser'  # Placeholder

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get user's friends
            cursor.execute("""
                SELECT u.username, u.full_name, u.profile_picture
                FROM friendships f
                JOIN users u ON (u.username = f.friend_username OR u.username = f.username)
                WHERE (f.username = %s OR f.friend_username = %s)
                AND f.status = 'accepted'
                AND u.username != %s
            """, (current_username, current_username, current_username))

            friends = []
            for row in cursor.fetchall():
                friends.append({
                    'username': row[0],
                    'full_name': row[1],
                    'profile_picture': row[2],
                    'is_online': False,  # TODO: Implement online status
                    'last_workout': 'Unknown',  # TODO: Get last workout
                    'mutual_friends': 0  # TODO: Calculate mutual friends
                })

            # Get friend suggestions (users not already friends)
            cursor.execute("""
                SELECT u.username, u.full_name, u.profile_picture
                FROM users u
                WHERE u.username != %s
                AND u.username NOT IN (
                    SELECT CASE
                        WHEN f.username = %s THEN f.friend_username
                        ELSE f.username
                    END
                    FROM friendships f
                    WHERE (f.username = %s OR f.friend_username = %s)
                )
                AND u.is_active = true
                LIMIT 5
            """, (current_username, current_username, current_username, current_username))

            suggestions = []
            for row in cursor.fetchall():
                suggestions.append({
                    'username': row[0],
                    'full_name': row[1],
                    'profile_picture': row[2],
                    'mutual_friends': 0,
                    'reason': 'New to WibeFit'
                })

            conn.close()

            response = {
                'friends': friends,
                'suggestions': suggestions
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Friends error: {e}")
            self.send_error(500, "Failed to fetch friends")

    def handle_discover_users_get(self):
        """Handle GET /api/community/users/discover"""
        try:
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            limit = int(query_params.get('limit', [10])[0])

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get users for discovery (active users with workout activity)
            cursor.execute("""
                SELECT
                    u.username, u.full_name, u.bio,
                    COUNT(DISTINCT f1.friend_username) as followers_count,
                    COUNT(DISTINCT f2.username) as following_count,
                    COUNT(DISTINCT ws.id) as posts_count
                FROM users u
                LEFT JOIN friendships f1 ON u.username = f1.username AND f1.status = 'accepted'
                LEFT JOIN friendships f2 ON u.username = f2.friend_username AND f2.status = 'accepted'
                LEFT JOIN workout_sessions ws ON u.username = ws.username
                WHERE u.is_active = true
                GROUP BY u.username, u.full_name, u.bio
                HAVING COUNT(DISTINCT ws.id) > 0
                ORDER BY followers_count DESC, posts_count DESC
                LIMIT %s
            """, (limit,))

            users = []
            for row in cursor.fetchall():
                users.append({
                    'username': row[0],
                    'full_name': row[1],
                    'bio': row[2],
                    'followers_count': row[3],
                    'following_count': row[4],
                    'posts_count': row[5],
                    'is_following': False,  # TODO: Check if current user follows this user
                    'profile_picture': None
                })

            conn.close()

            response = {'users': users}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Discover users error: {e}")
            self.send_error(500, "Failed to fetch discover users")

    def handle_search_users_get(self):
        """Handle GET /api/community/users/search"""
        try:
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            search_query = query_params.get('q', [''])[0]
            limit = int(query_params.get('limit', [10])[0])

            if not search_query:
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'users': []}).encode())
                return

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Search users by username or full name
            cursor.execute("""
                SELECT username, full_name, bio
                FROM users
                WHERE (username ILIKE %s OR full_name ILIKE %s)
                AND is_active = true
                ORDER BY
                    CASE WHEN username ILIKE %s THEN 1 ELSE 2 END,
                    username
                LIMIT %s
            """, (f'%{search_query}%', f'%{search_query}%', f'{search_query}%', limit))

            users = []
            for row in cursor.fetchall():
                users.append({
                    'username': row[0],
                    'full_name': row[1],
                    'bio': row[2],
                    'profile_picture': None
                })

            conn.close()

            response = {'users': users}
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Search users error: {e}")
            self.send_error(500, "Failed to search users")

    def handle_login_username(self, data):
        """Handle POST /api/auth/login-username"""
        try:
            username = data.get('username')
            password = data.get('password')

            print(f"🔍 Login attempt - Username: {username}, Password length: {len(password) if password else 0}")

            if not username or not password:
                print("❌ Missing username or password")
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Username and password required"}).encode())
                return

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Find user by username
            cursor.execute("""
                SELECT username, email, full_name, password_hash, role, is_active
                FROM users
                WHERE username = %s
            """, (username,))

            user_row = cursor.fetchone()
            conn.close()

            print(f"🔍 Database query result: {user_row is not None}")

            if not user_row:
                print(f"❌ User '{username}' not found in database")
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Invalid username or password"}).encode())
                return

            username_db, email, full_name, password_hash, role, is_active = user_row
            print(f"🔍 Found user: {username_db}, has_password: {password_hash is not None}, is_active: {is_active}")

            if not is_active:
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Account is inactive"}).encode())
                return

            # For demo purposes, accept any password if no hash is stored
            # In production, you'd verify the password hash
            if password_hash:
                password_valid = verify_password(password, password_hash)
                print(f"🔍 Password verification: {password_valid}")
                if not password_valid:
                    print(f"❌ Invalid password for user '{username}'")
                    self.send_response(401)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Invalid username or password"}).encode())
                    return
            else:
                print("⚠️ No password hash stored, accepting any password")

            # Generate access token
            access_token = secrets.token_urlsafe(32)

            # Store token in database (simplified - in production use proper token management)
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE username = %s
            """, (username,))
            conn.commit()
            conn.close()

            response = {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "username": username_db,
                    "email": email,
                    "full_name": full_name,
                    "role": role
                }
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Username login error: {e}")
            self.send_error(500, "Login failed")

    def handle_register_username(self, data):
        """Handle POST /api/auth/register-username"""
        try:
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                self.send_error(400, "Username and password required")
                return

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Check if username already exists
            cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
            existing_user = cursor.fetchone()
            print(f"🔍 Registration check for '{username}': existing_user = {existing_user}")
            if existing_user:
                conn.close()
                print(f"❌ Username '{username}' already exists in database")
                self.send_error(409, "Username already exists")
                return

            # Hash password
            password_hash = hash_password(password)

            # Create user
            cursor.execute("""
                INSERT INTO users (username, email, full_name, password_hash, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            """, (username, f"{username}@wibefit.local", username.title(), password_hash, 'user', True))

            # Create empty profile for the new user
            cursor.execute("""
                INSERT INTO user_profiles (username, profile_setup_completed, created_at, updated_at)
                VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, (username, False))

            conn.commit()
            conn.close()

            print(f"✅ USER REGISTERED: {username} with empty profile created")

            # Generate access token
            access_token = secrets.token_urlsafe(32)

            response = {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "username": username,
                    "email": f"{username}@wibefit.local",
                    "full_name": username.title(),
                    "role": "user"
                }
            }

            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Username registration error: {e}")
            self.send_error(500, "Registration failed")

    def handle_users_me_get(self):
        """Handle GET /api/users/me"""
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get the most recently logged-in user (latest login or demo user as fallback)
            cursor.execute("""
                SELECT u.username, u.email, u.full_name, u.role, u.is_active
                FROM users u
                LEFT JOIN user_profiles up ON u.username = up.username
                WHERE u.is_active = true
                ORDER BY u.last_login_at DESC NULLS LAST, u.username = 'demo' DESC
                LIMIT 1
            """)

            user_row = cursor.fetchone()

            if user_row:
                response = {
                    "id": 1,
                    "username": user_row[0],
                    "email": user_row[1],
                    "full_name": user_row[2],
                    "role": user_row[3],
                    "status": "success"
                }
                print(f"✅ Current user: {user_row[0]} ({user_row[2]})")
            else:
                # Fallback to demo user
                response = {
                    "id": 1,
                    "username": "demo",
                    "email": "<EMAIL>",
                    "full_name": "Demo User",
                    "role": "user",
                    "status": "success"
                }
                print(f"⚠️ No active user found, using demo user")

            conn.close()

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Users me error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch user"}).encode())

    def handle_users_me_stats_get(self):
        """Handle GET /api/users/me/stats"""
        try:
            response = {
                "total_workouts": 0,
                "total_calories": 0,
                "total_time": 0,
                "streak": 0,
                "status": "success"
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            print(f"❌ Users stats error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch stats"}).encode())

    def handle_users_me_profile_get(self):
        """Handle GET /api/users/me/profile"""
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get the profile for the most recently active user
            cursor.execute("""
                SELECT up.username, up.age, up.height, up.current_weight, up.fitness_level,
                       up.primary_goals, up.equipment_access, up.preferred_workout_days,
                       up.gender, up.profile_setup_completed, up.created_at,
                       u.email, up.phone, up.activity_level, up.preferred_workout_duration
                FROM user_profiles up
                JOIN users u ON up.username = u.username
                WHERE u.is_active = true
                ORDER BY up.updated_at DESC, u.username = 'demo' DESC
                LIMIT 1
            """)

            profile_row = cursor.fetchone()

            if profile_row:
                response = {
                    "age": profile_row[1],
                    "height": profile_row[2],
                    "current_weight": profile_row[3],
                    "fitness_level": profile_row[4],
                    "primary_goals": profile_row[5] if profile_row[5] else [],
                    "equipment_access": profile_row[6] if profile_row[6] else [],
                    "preferred_workout_days": profile_row[7] if profile_row[7] else [],
                    "gender": profile_row[8],
                    "profile_setup_completed": profile_row[9],
                    "email": profile_row[11],
                    "phone": profile_row[12],
                    "activity_level": profile_row[13],
                    "preferred_workout_duration": profile_row[14],
                    "status": "success"
                }
                print(f"✅ Real profile data loaded for user: {profile_row[0]}")
            else:
                # Return empty profile if none found
                response = {
                    "age": None,
                    "height": None,
                    "current_weight": None,
                    "fitness_level": None,
                    "primary_goals": [],
                    "equipment_access": [],
                    "preferred_workout_days": [],
                    "gender": None,
                    "profile_setup_completed": False,
                    "email": None,
                    "phone": None,
                    "activity_level": None,
                    "preferred_workout_duration": None,
                    "status": "success"
                }
                print(f"ℹ️ No profile found in database - returning empty profile")

            conn.close()

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            print(f"❌ Users profile error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch profile"}).encode())

    def handle_community_users_get(self):
        """Handle GET /api/community/users/*/followers and /api/community/users/*/following"""
        try:
            response = {
                "count": 0,
                "users": [],
                "status": "success"
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            print(f"❌ Community users error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch community data"}).encode())

    def handle_exercises_favorites_get(self):
        """Handle GET /api/workouts/exercises/favorites"""
        try:
            # Parse query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            limit = int(query_params.get('limit', [100])[0])
            offset = int(query_params.get('offset', [0])[0])

            response = {
                "exercises": [],
                "total": 0,
                "limit": limit,
                "offset": offset,
                "status": "success"
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Favorites returned: 0 exercises")
        except Exception as e:
            print(f"❌ Exercises favorites error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch favorites"}).encode())

    def handle_workouts_exercises_get(self):
        """Handle GET /api/workouts/exercises"""
        try:
            # Parse query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            limit = int(query_params.get('limit', [20])[0])
            offset = int(query_params.get('offset', [0])[0])

            # Parse filter parameters
            search = query_params.get('q', [None])[0]
            muscle_groups = query_params.get('muscle_groups', [None])[0]
            equipment = query_params.get('equipment', [None])[0]
            difficulty = query_params.get('difficulty', [None])[0]
            category = query_params.get('category', [None])[0]

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Build WHERE clause with filters
            where_conditions = ["is_active = true"]
            params = []

            if search:
                where_conditions.append("(name ILIKE %s OR description ILIKE %s)")
                params.extend([f"%{search}%", f"%{search}%"])

            if muscle_groups:
                # Handle comma-separated muscle groups
                muscle_list = [mg.strip().title() for mg in muscle_groups.split(',')]
                where_conditions.append("muscle_groups ?| %s")
                params.append(muscle_list)

            if equipment:
                # Handle comma-separated equipment
                equipment_list = [eq.strip().title() for eq in equipment.split(',')]
                where_conditions.append("equipment ?| %s")
                params.append(equipment_list)

            if difficulty:
                where_conditions.append("difficulty = %s")
                params.append(difficulty.lower())

            if category:
                where_conditions.append("category = %s")
                params.append(category.lower())

            where_clause = " AND ".join(where_conditions)

            # Get total count with filters
            count_query = f"SELECT COUNT(*) FROM exercises WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # Get exercises with pagination and filters
            exercise_query = f"""
                SELECT id, name, description, muscle_groups, equipment, difficulty,
                       exercise_type, category, duration_minutes, calories_per_minute,
                       video_url, image_url, created_at
                FROM exercises
                WHERE {where_clause}
                ORDER BY name
                LIMIT %s OFFSET %s
            """
            cursor.execute(exercise_query, params + [limit, offset])

            exercises = []
            for row in cursor.fetchall():
                exercise = {
                    "id": row[0],
                    "name": row[1] or "",
                    "slug": (row[1] or "").lower().replace(" ", "-").replace("_", "-"),
                    "description": row[2] or "",
                    "muscle_groups": row[3] if row[3] else [],
                    "equipment": row[4] if row[4] else [],
                    "difficulty": row[5] or "beginner",
                    "exercise_type": row[6] or "strength",
                    "category": row[7] or "general",
                    "duration_minutes": row[8] or 0,
                    "calories_per_minute": row[9] or 0,
                    "video_url": row[10] or "",
                    "image_url": row[11] or "",
                    "is_active": True,
                    "created_at": row[12].isoformat() if row[12] else None
                }
                exercises.append(exercise)

            conn.close()

            response = {
                "exercises": exercises,
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + len(exercises)) < total_count,
                "status": "success"
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Exercises returned: {len(exercises)} of {total_count} total")

        except Exception as e:
            print(f"❌ Workouts exercises error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch exercises"}).encode())

    def handle_exercises_get(self):
        """Handle GET /api/exercises"""
        try:
            # Parse query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            limit = int(query_params.get('limit', [20])[0])
            offset = int(query_params.get('offset', [0])[0])

            # Parse filter parameters
            search = query_params.get('q', [None])[0]
            muscle_groups = query_params.get('muscle_groups', [None])[0]
            equipment = query_params.get('equipment', [None])[0]
            difficulty = query_params.get('difficulty', [None])[0]
            category = query_params.get('category', [None])[0]

            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Build WHERE clause with filters
            where_conditions = ["is_active = true"]
            params = []

            if search:
                where_conditions.append("(name ILIKE %s OR description ILIKE %s)")
                params.extend([f"%{search}%", f"%{search}%"])

            if muscle_groups:
                muscle_list = [mg.strip().title() for mg in muscle_groups.split(',')]
                where_conditions.append("muscle_groups ?| %s")
                params.append(muscle_list)

            if equipment:
                equipment_list = [eq.strip().title() for eq in equipment.split(',')]
                where_conditions.append("equipment ?| %s")
                params.append(equipment_list)

            if difficulty:
                where_conditions.append("difficulty = %s")
                params.append(difficulty.lower())

            if category:
                where_conditions.append("category = %s")
                params.append(category.lower())

            where_clause = " AND ".join(where_conditions)

            # Get total count with filters
            count_query = f"SELECT COUNT(*) FROM exercises WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # Get exercises with pagination and filters
            exercise_query = f"""
                SELECT id, name, description, muscle_groups, equipment, difficulty,
                       exercise_type, category, duration_minutes, calories_per_minute,
                       video_url, image_url, created_at
                FROM exercises
                WHERE {where_clause}
                ORDER BY name
                LIMIT %s OFFSET %s
            """
            cursor.execute(exercise_query, params + [limit, offset])

            exercises = []
            for row in cursor.fetchall():
                exercise = {
                    "id": row[0],
                    "name": row[1] or "",
                    "slug": (row[1] or "").lower().replace(" ", "-").replace("_", "-"),
                    "description": row[2] or "",
                    "muscle_groups": row[3] if row[3] else [],
                    "equipment": row[4] if row[4] else [],
                    "difficulty": row[5] or "beginner",
                    "exercise_type": row[6] or "strength",
                    "category": row[7] or "general",
                    "duration_minutes": row[8] or 0,
                    "calories_per_minute": row[9] or 0,
                    "video_url": row[10] or "",
                    "image_url": row[11] or "",
                    "is_active": True,
                    "created_at": row[12].isoformat() if row[12] else None
                }
                exercises.append(exercise)

            conn.close()

            response = {
                "exercises": exercises,
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + len(exercises)) < total_count,
                "status": "success"
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            print(f"✅ Exercises returned: {len(exercises)} of {total_count} total")

        except Exception as e:
            print(f"❌ Exercises error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Failed to fetch exercises"}).encode())

    def handle_ai_workout_generation(self, data):
        """Handle AI workout plan generation"""
        try:
            print(f"🤖 AI Workout Generation Request: {data}")

            # Extract user profile data
            user_profile = data.get('user_profile', {})
            plan_type = data.get('plan_type', '4_day_split')
            duration_weeks = data.get('duration_weeks', 4)

            print(f"👤 User Profile: {user_profile}")
            print(f"📋 Plan Type: {plan_type}")
            print(f"⏱️ Duration: {duration_weeks} weeks")

            # Generate AI workout plan based on user profile
            ai_plan = self.generate_ai_enhanced_workout_plan(
                user_profile=user_profile,
                plan_type=plan_type,
                duration_weeks=duration_weeks
            )

            print(f"✅ AI Plan Generated: {len(ai_plan.get('workouts', []))} workouts")

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_cors_headers()
            self.end_headers()
            self.wfile.write(json.dumps(ai_plan).encode())

        except Exception as e:
            print(f"❌ AI Workout Generation Error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_cors_headers()
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def handle_ai_exercise_selection(self, data):
        """Handle AI exercise selection for workout setup"""
        log_request('POST', '/api/ai/select-exercises', data)

        try:
            # Extract parameters
            day_name = data.get('day_name', 'Day 1')
            muscle_groups = data.get('muscle_groups', [])
            user_profile = data.get('user_profile', {})
            exercise_count = data.get('exercise_count', 6)

            print(f"🤖 AI Exercise Selection Request:")
            print(f"📅 Day: {day_name}")
            print(f"💪 Muscle Groups: {muscle_groups}")
            print(f"👤 User Profile: {user_profile}")
            print(f"🔢 Exercise Count: {exercise_count}")

            # Get AI-selected exercises from database
            selected_exercises = self.select_exercises_for_day(
                muscle_groups, user_profile, exercise_count
            )

            response = {
                'day_name': day_name,
                'muscle_groups': muscle_groups,
                'exercises': selected_exercises,
                'total_exercises': len(selected_exercises),
                'estimated_duration': len(selected_exercises) * 5,  # 5 min per exercise
                'estimated_calories': len(selected_exercises) * 25   # 25 cal per exercise
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

            print(f"✅ AI Exercise Selection: {len(selected_exercises)} exercises selected")

        except Exception as e:
            print(f"❌ AI Exercise Selection Error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def select_exercises_for_day(self, muscle_groups, user_profile, exercise_count):
        """Select exercises from database based on muscle groups and user profile"""
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Build muscle group filter
            muscle_group_conditions = []
            params = []

            if muscle_groups:
                for muscle in muscle_groups:
                    muscle_group_conditions.append("muscle_groups ? %s")
                    params.append(muscle.title())

            # Get user equipment preferences
            equipment_access = user_profile.get('equipment_access', [])
            equipment_conditions = []

            if equipment_access:
                for equipment in equipment_access:
                    equipment_conditions.append("equipment ? %s")
                    params.append(equipment.title())

            # Build WHERE clause
            where_conditions = ["is_active = true"]

            if muscle_group_conditions:
                where_conditions.append(f"({' OR '.join(muscle_group_conditions)})")

            if equipment_conditions:
                where_conditions.append(f"({' OR '.join(equipment_conditions)})")

            # Get user fitness level for difficulty filtering
            fitness_level = user_profile.get('fitness_level', 'intermediate')
            if fitness_level == 'beginner':
                where_conditions.append("difficulty IN ('beginner', 'easy')")
            elif fitness_level == 'advanced':
                where_conditions.append("difficulty IN ('intermediate', 'advanced', 'hard')")
            else:  # intermediate
                where_conditions.append("difficulty IN ('beginner', 'intermediate', 'medium')")

            where_clause = " AND ".join(where_conditions)

            # Query exercises with randomization for variety
            query = f"""
                SELECT id, name, description, muscle_groups, equipment, difficulty,
                       exercise_type, category, duration_minutes, calories_per_minute,
                       video_url, image_url
                FROM exercises
                WHERE {where_clause}
                ORDER BY RANDOM()
                LIMIT %s
            """

            params.append(exercise_count)
            cursor.execute(query, params)

            exercises = []
            for row in cursor.fetchall():
                exercise = {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'muscle_groups': row[3],
                    'equipment': row[4],
                    'difficulty': row[5],
                    'exercise_type': row[6],
                    'category': row[7],
                    'duration_minutes': row[8] or 5,
                    'calories_per_minute': row[9] or 5.0,
                    'video_url': row[10],
                    'image_url': row[11],
                    'sets': 3,  # Default sets
                    'reps': 12,  # Default reps
                    'rest_seconds': 60  # Default rest
                }
                exercises.append(exercise)

            conn.close()

            print(f"🎯 Selected {len(exercises)} exercises for {muscle_groups}")
            return exercises

        except Exception as e:
            print(f"❌ Exercise selection error: {e}")
            return []

    def generate_personalized_workout_plan(self, fitness_level, goals, equipment, age, weight, plan_type, duration_weeks):
        """Generate personalized workout plan using AI logic"""
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # Get exercises from database based on user profile
            equipment_filter = []
            if equipment:
                if isinstance(equipment, list):
                    equipment_filter = equipment
                else:
                    equipment_filter = [equipment]

            # Build equipment filter for SQL (equipment is JSONB) - simplified for now
            equipment_sql = ""
            # For now, let's not filter by equipment to get some exercises
            # if equipment_filter:
            #     equipment_conditions = []
            #     for eq in equipment_filter:
            #         equipment_conditions.append(f"equipment ? '{eq}'")
            #     equipment_sql = f"AND ({' OR '.join(equipment_conditions)} OR equipment ? 'None')"

            # Get exercises based on fitness level and equipment
            difficulty_map = {
                'beginner': ['beginner'],
                'intermediate': ['beginner', 'intermediate'],
                'advanced': ['intermediate', 'advanced', 'expert']
            }

            allowed_difficulties = difficulty_map.get(fitness_level, ['Beginner', 'Easy'])
            difficulty_placeholders = ','.join(['%s'] * len(allowed_difficulties))

            # Query exercises
            query = f"""
                SELECT id, name, category, muscle_groups, equipment,
                       difficulty, instructions, duration_minutes, calories_per_minute
                FROM exercises
                WHERE is_active = true
                AND difficulty IN ({difficulty_placeholders})
                {equipment_sql}
                ORDER BY RANDOM()
                LIMIT 50
            """

            params = allowed_difficulties
            cursor.execute(query, params)
            available_exercises = cursor.fetchall()

            conn.close()

            # Generate 4-day workout plan
            workout_plan = {
                "plan_id": f"ai_plan_{int(datetime.now().timestamp())}",
                "plan_name": f"AI Personalized {plan_type.replace('_', ' ').title()}",
                "duration_weeks": duration_weeks,
                "fitness_level": fitness_level,
                "goals": goals,
                "equipment": equipment,
                "workouts": []
            }

            # Define workout structure for 4-day split
            workout_days = [
                {"name": "Upper Body Strength", "focus": ["Chest", "Back", "Shoulders", "Arms"]},
                {"name": "Lower Body Power", "focus": ["Legs", "Glutes", "Calves"]},
                {"name": "Core & Cardio", "focus": ["Core", "Cardio"]},
                {"name": "Full Body Circuit", "focus": ["Full Body", "Functional"]}
            ]

            for day_idx, workout_day in enumerate(workout_days):
                # Select exercises for this workout day
                day_exercises = []
                target_muscles = workout_day["focus"]

                # Filter exercises by muscle groups
                suitable_exercises = []
                for exercise in available_exercises:
                    muscle_groups = exercise[3] if exercise[3] else []
                    if isinstance(muscle_groups, str):
                        muscle_groups = [muscle_groups]

                    # Check if exercise targets any of the focus muscles
                    if any(muscle in str(muscle_groups).lower() for muscle in [m.lower() for m in target_muscles]):
                        suitable_exercises.append(exercise)

                # If no specific exercises found, use general exercises
                if not suitable_exercises:
                    suitable_exercises = available_exercises[:8]

                # Select 5-8 exercises for the workout
                import random
                selected_exercises = random.sample(suitable_exercises, min(6, len(suitable_exercises)))

                total_calories = 0
                total_duration = 0

                for exercise in selected_exercises:
                    # Calculate sets and reps based on fitness level
                    if fitness_level == 'beginner':
                        sets = random.randint(2, 3)
                        reps = random.randint(8, 12)
                        duration = random.randint(3, 5)
                    elif fitness_level == 'intermediate':
                        sets = random.randint(3, 4)
                        reps = random.randint(10, 15)
                        duration = random.randint(4, 6)
                    else:  # advanced
                        sets = random.randint(4, 5)
                        reps = random.randint(12, 20)
                        duration = random.randint(5, 8)

                    calories_per_min = exercise[8] if exercise[8] else 5
                    exercise_calories = duration * calories_per_min

                    day_exercises.append({
                        "id": exercise[0],
                        "name": exercise[1],
                        "category": exercise[2],
                        "muscle_groups": exercise[3],
                        "equipment": exercise[4],
                        "difficulty": exercise[5],
                        "instructions": exercise[6],
                        "sets": sets,
                        "reps": reps,
                        "duration_minutes": duration,
                        "calories_burned": exercise_calories,
                        "rest_seconds": 60 if fitness_level == 'beginner' else 45
                    })

                    total_calories += exercise_calories
                    total_duration += duration

                workout_plan["workouts"].append({
                    "day": day_idx + 1,
                    "name": workout_day["name"],
                    "focus_areas": workout_day["focus"],
                    "total_duration_minutes": total_duration,
                    "estimated_calories": total_calories,
                    "exercises": day_exercises
                })

            return workout_plan

        except Exception as e:
            print(f"❌ Error generating workout plan: {e}")
            return {
                "error": "Failed to generate workout plan",
                "message": str(e)
            }

    def generate_ai_enhanced_workout_plan(self, user_profile, plan_type, duration_weeks):
        """Generate AI-enhanced workout plan using OpenAI/OpenRouter API"""
        try:
            # Get API key from environment
            openai_api_key = os.getenv('OPENAI_API_KEY')
            openrouter_api_key = os.getenv('OPENROUTER_API_KEY')

            if not openai_api_key and not openrouter_api_key:
                print("⚠️ No AI API key found, using database-based generation")
                return self.generate_personalized_workout_plan(
                    fitness_level=user_profile.get('fitness_level', 'intermediate'),
                    goals=user_profile.get('primary_goals', []),
                    equipment=user_profile.get('equipment_access', []),
                    age=user_profile.get('age', 25),
                    weight=user_profile.get('current_weight', 70),
                    plan_type=plan_type,
                    duration_weeks=duration_weeks
                )

            # Get available exercises from database
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, name, category, muscle_groups, equipment,
                       difficulty, instructions, duration_minutes, calories_per_minute
                FROM exercises
                WHERE is_active = true
                ORDER BY RANDOM()
                LIMIT 100
            """)
            available_exercises = cursor.fetchall()
            conn.close()

            # Prepare AI prompt
            exercises_info = []
            for ex in available_exercises[:20]:  # Limit for prompt size
                exercises_info.append({
                    "id": ex[0],
                    "name": ex[1],
                    "category": ex[2],
                    "muscle_groups": ex[3],
                    "equipment": ex[4],
                    "difficulty": ex[5]
                })

            ai_prompt = f"""
            Create a personalized {plan_type} workout plan for a user with the following profile:
            - Fitness Level: {user_profile.get('fitness_level', 'intermediate')}
            - Goals: {', '.join(user_profile.get('primary_goals', []))}
            - Available Equipment: {', '.join(user_profile.get('equipment_access', []))}
            - Age: {user_profile.get('age', 25)}
            - Weight: {user_profile.get('current_weight', 70)}kg
            - Duration: {duration_weeks} weeks

            Available exercises (select from these): {json.dumps(exercises_info[:10])}

            Create a 4-day workout plan with the following structure:
            Day 1: Upper Body Strength
            Day 2: Lower Body Power
            Day 3: Core & Cardio
            Day 4: Full Body Circuit

            For each day, select 5-6 exercises and specify sets, reps, and estimated duration.
            Return ONLY a JSON response in this exact format:
            {{
                "plan_id": "ai_plan_timestamp",
                "plan_name": "AI Personalized 4 Day Split",
                "duration_weeks": {duration_weeks},
                "fitness_level": "{user_profile.get('fitness_level', 'intermediate')}",
                "goals": {json.dumps(user_profile.get('primary_goals', []))},
                "equipment": {json.dumps(user_profile.get('equipment_access', []))},
                "workouts": [
                    {{
                        "day": 1,
                        "name": "Upper Body Strength",
                        "focus_areas": ["Chest", "Back", "Shoulders", "Arms"],
                        "total_duration_minutes": 45,
                        "estimated_calories": 400,
                        "exercises": [
                            {{
                                "id": "exercise_id",
                                "name": "Exercise Name",
                                "sets": 3,
                                "reps": 12,
                                "duration_minutes": 5,
                                "calories_burned": 50,
                                "category": "Strength",
                                "difficulty": "intermediate"
                            }}
                        ]
                    }}
                ]
            }}
            """

            # Try OpenRouter first (since we have that key), then OpenAI
            ai_response = None
            if openrouter_api_key:
                print("🤖 Using OpenRouter API for AI workout generation")
                ai_response = self._call_openrouter_api(ai_prompt, openrouter_api_key)
            elif openai_api_key:
                print("🤖 Using OpenAI API for AI workout generation")
                ai_response = self._call_openai_api(ai_prompt, openai_api_key)

            if ai_response:
                try:
                    # Parse AI response
                    ai_plan = json.loads(ai_response)
                    print(f"✅ AI-generated workout plan created successfully")
                    return ai_plan
                except json.JSONDecodeError:
                    print("❌ Failed to parse AI response, falling back to database generation")

            # Fallback to database generation
            return self.generate_personalized_workout_plan(
                fitness_level=user_profile.get('fitness_level', 'intermediate'),
                goals=user_profile.get('primary_goals', []),
                equipment=user_profile.get('equipment_access', []),
                age=user_profile.get('age', 25),
                weight=user_profile.get('current_weight', 70),
                plan_type=plan_type,
                duration_weeks=duration_weeks
            )

        except Exception as e:
            print(f"❌ AI workout generation error: {e}")
            # Fallback to database generation
            return self.generate_personalized_workout_plan(
                fitness_level=user_profile.get('fitness_level', 'intermediate'),
                goals=user_profile.get('primary_goals', []),
                equipment=user_profile.get('equipment_access', []),
                age=user_profile.get('age', 25),
                weight=user_profile.get('current_weight', 70),
                plan_type=plan_type,
                duration_weeks=duration_weeks
            )

    def _call_openai_api(self, prompt, api_key):
        """Call OpenAI API for workout plan generation"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': 'You are a professional fitness trainer and workout planner. Always respond with valid JSON only.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 2000,
                'temperature': 0.7
            }

            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"❌ OpenAI API error: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ OpenAI API call failed: {e}")
            return None

    def _call_openrouter_api(self, prompt, api_key):
        """Call OpenRouter API for workout plan generation"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://wibefit.ai',  # Your site URL for rankings
                'X-Title': 'WibeFit AI Workout Planner'  # Your site title for rankings
            }

            data = {
                'model': os.getenv('OPENROUTER_MODEL', 'google/gemini-2.5-flash'),
                'messages': [
                    {'role': 'system', 'content': 'You are a professional fitness trainer and workout planner. Always respond with valid JSON only.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 2000,
                'temperature': 0.7
            }

            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"❌ OpenRouter API error: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ OpenRouter API call failed: {e}")
            return None

def run_postgresql_server(port=8000):
    """Run the PostgreSQL server"""
    if not init_postgresql_database():
        print("❌ Failed to initialize PostgreSQL database")
        print("Please check your PostgreSQL connection settings")
        return
    
    server_address = ('', port)
    httpd = HTTPServer(server_address, WibeFitPostgreSQLHandler)
    
    print(f"🚀 WibeFit v1.0 PostgreSQL Backend Server starting...")
    print(f"📍 Server running at:")
    print(f"   - Local: http://localhost:{port}")
    print(f"   - Network: http://0.0.0.0:{port}")
    print(f"   - Android Emulator: http://********:{port}")
    print(f"   - Health: http://localhost:{port}/health")
    print(f"   - Debug: http://localhost:{port}/api/debug")
    print(f"💾 Database: PostgreSQL ({DB_CONFIG['database']})")
    print(f"")
    print(f"🔑 Demo Login Credentials:")
    print(f"   - Username: demo")
    print(f"   - Password: demo123")
    print(f"")
    print(f"🤖 Android Emulator Support: ENABLED")
    print(f"📱 iOS Simulator Support: ENABLED")
    print("")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_postgresql_server()
