#!/usr/bin/env python3
"""
Simple WibeFit Backend Server for Testing
This is a minimal server to test profile functionality
"""

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import json
import os
from datetime import datetime

# Create FastAPI app
app = FastAPI(
    title="WibeFit API (Simple)",
    description="Simple backend for testing profile functionality",
    version="1.0.0"
)

# CORS middleware for Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:*",
        "http://***********:*",
        "http://192.168.1.*:*",
        "*",  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage for testing
users_db = {}
profiles_db = {}

# Pydantic models
class UserProfileUpdate(BaseModel):
    age: Optional[int] = None
    height: Optional[float] = None
    current_weight: Optional[float] = None
    fitness_level: Optional[str] = None
    primary_goals: Optional[List[str]] = None
    equipment_access: Optional[List[str]] = None
    preferred_workout_days: Optional[List[str]] = None
    gender: Optional[str] = None

class LoginRequest(BaseModel):
    email: str
    password: str

class RegisterRequest(BaseModel):
    email: str
    password: str
    full_name: str

# Mock authentication
def get_current_user(authorization: str = None):
    # For testing, accept any token and return a mock user
    # In a real app, you would validate the JWT token here
    return {
        "id": 1,
        "email": "<EMAIL>",
        "full_name": "Demo User"
    }

# Health check endpoint
@app.get("/")
async def root():
    return {
        "message": "WibeFit Simple API is running!",
        "version": "1.0.0",
        "environment": "development"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "connected (in-memory)",
            "gcs": "disconnected",
            "openrouter": "disconnected"
        }
    }

# Auth endpoints
@app.post("/api/auth/login")
async def login(request: LoginRequest):
    # Mock login - accept any credentials
    user = {
        "id": 1,
        "email": request.email,
        "full_name": "Demo User",
        "role": "member"
    }
    
    return {
        "access_token": "mock_token_12345",
        "token_type": "bearer",
        "user": user
    }

@app.post("/api/auth/register")
async def register(request: RegisterRequest):
    # Mock registration
    user = {
        "id": 1,
        "email": request.email,
        "full_name": request.full_name,
        "role": "member"
    }
    
    return {
        "access_token": "mock_token_12345",
        "token_type": "bearer",
        "user": user
    }

@app.get("/api/auth/me")
async def get_current_user_info():
    return {
        "id": 1,
        "email": "<EMAIL>",
        "full_name": "Demo User",
        "role": "member",
        "is_active": True,
        "is_verified": True,
        "created_at": datetime.utcnow().isoformat()
    }

# Profile endpoints
@app.put("/api/auth/profile")
@app.post("/api/auth/profile")
async def update_profile(profile_data: UserProfileUpdate):
    """Update user profile data in database"""
    try:
        user_id = 1  # Mock user ID
        
        # Store profile data in memory
        if user_id not in profiles_db:
            profiles_db[user_id] = {}
        
        # Update profile fields
        profile_dict = profile_data.model_dump(exclude_unset=True)
        profiles_db[user_id].update(profile_dict)
        profiles_db[user_id]["updated_at"] = datetime.utcnow().isoformat()
        
        print(f"✅ Profile updated for user {user_id}: {profile_dict}")
        
        return {
            "message": "Profile updated successfully",
            "profile": {
                "id": 1,
                "user_id": user_id,
                **profiles_db[user_id]
            }
        }
    except Exception as e:
        print(f"❌ Error updating profile: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update profile: {str(e)}"
        )

@app.get("/api/auth/profile")
async def get_profile():
    """Get user profile data from database"""
    try:
        user_id = 1  # Mock user ID
        
        if user_id in profiles_db:
            return {
                "profile": {
                    "id": 1,
                    "user_id": user_id,
                    **profiles_db[user_id]
                }
            }
        else:
            raise HTTPException(
                status_code=404,
                detail="Profile not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error fetching profile: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch profile: {str(e)}"
        )

# Community endpoints (mock)
@app.get("/api/community/posts")
async def get_community_posts():
    return {"posts": []}

@app.post("/api/community/posts")
async def create_community_post():
    return {"message": "Post created successfully"}

# Workouts endpoints (mock)
@app.get("/api/workouts")
async def get_workouts():
    return {"workouts": []}

if __name__ == "__main__":
    print("🚀 Starting WibeFit Simple Backend Server...")
    print("📍 Server will be available at:")
    print("   - Local: http://localhost:8000")
    print("   - Network: http://***********:8000")
    print("   - Health check: http://***********:8000/health")
    print("")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
