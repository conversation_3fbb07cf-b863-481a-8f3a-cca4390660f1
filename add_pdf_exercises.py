#!/usr/bin/env python3
"""
Add exercises from the extracted PDF to the database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.exercise import Exercise
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def categorize_exercise(name):
    """Categorize exercise based on name patterns"""
    name_lower = name.lower()
    
    # Core/Abs exercises
    if any(keyword in name_lower for keyword in [
        'sit-up', 'sit up', 'crunch', 'plank', 'ab ', 'abs', 'core', 'twist', 'bicycle',
        'heel touch', 'leg raise', 'knee to elbow', 'dead bug', 'mountain climber',
        'russian twist', 'v-up', 'hollow', 'tuck', 'roll out', 'wheel'
    ]):
        return 'core', ['Core', 'Abs']
    
    # Upper body exercises
    elif any(keyword in name_lower for keyword in [
        'push', 'pull', 'press', 'row', 'chest', 'shoulder', 'arm', 'tricep', 'bicep',
        'lat', 'back', 'bench', 'dip', 'fly', 'raise', 'curl', 'extension'
    ]):
        return 'upper_body', ['Chest', 'Arms', 'Shoulders']
    
    # Lower body exercises  
    elif any(keyword in name_lower for keyword in [
        'squat', 'lunge', 'leg', 'glute', 'hip', 'thigh', 'calf', 'quad', 'hamstring',
        'step', 'jump', 'hop', 'bridge', 'deadlift', 'kick'
    ]):
        return 'lower_body', ['Legs', 'Glutes']
    
    # Cardio exercises
    elif any(keyword in name_lower for keyword in [
        'run', 'jog', 'sprint', 'cardio', 'aerobic', 'jumping', 'burpee', 'jack',
        'high knee', 'butt kick', 'step up'
    ]):
        return 'cardio', ['Full Body']
    
    # Stretching/Flexibility
    elif any(keyword in name_lower for keyword in [
        'stretch', 'yoga', 'pose', 'hold', 'flexibility', 'mobility'
    ]):
        return 'flexibility', ['Full Body']
    
    # Default to core if uncertain
    else:
        return 'core', ['Core']

def determine_equipment(name):
    """Determine equipment based on exercise name"""
    name_lower = name.lower()
    
    if any(keyword in name_lower for keyword in [
        'dumbbell', 'db ', 'weight', 'barbell', 'bb '
    ]):
        return ['Dumbbells']
    elif any(keyword in name_lower for keyword in [
        'resistance band', 'band', 'elastic'
    ]):
        return ['Resistance Bands']
    elif any(keyword in name_lower for keyword in [
        'kettlebell', 'kb '
    ]):
        return ['Kettlebell']
    elif any(keyword in name_lower for keyword in [
        'cable', 'machine', 'lat pulldown', 'leg press'
    ]):
        return ['Cable Machine']
    elif any(keyword in name_lower for keyword in [
        'bench', 'incline', 'decline'
    ]):
        return ['Bench']
    elif any(keyword in name_lower for keyword in [
        'ab wheel', 'wheel'
    ]):
        return ['Ab Wheel']
    elif any(keyword in name_lower for keyword in [
        'mat', 'floor'
    ]):
        return ['Exercise Mat']
    else:
        return ['None']

def determine_difficulty(name):
    """Determine difficulty based on exercise complexity"""
    name_lower = name.lower()
    
    if any(keyword in name_lower for keyword in [
        'advanced', 'explosive', 'plyometric', 'complex', 'weighted'
    ]):
        return 'advanced'
    elif any(keyword in name_lower for keyword in [
        'intermediate', 'single arm', 'single leg', 'unilateral'
    ]):
        return 'intermediate'
    else:
        return 'beginner'

def create_slug(name, session):
    """Create a unique slug for the exercise"""
    # Clean the name more thoroughly
    base_slug = re.sub(r'[^a-zA-Z0-9\s]', '', name.lower())
    base_slug = re.sub(r'\s+', '-', base_slug.strip())
    base_slug = base_slug[:50]  # Limit length

    # Check if slug exists and make it unique
    counter = 0
    slug = base_slug
    while True:
        existing = session.query(Exercise).filter_by(slug=slug).first()
        if not existing:
            break
        counter += 1
        slug = f"{base_slug}-{counter}"
        # Prevent infinite loop
        if counter > 1000:
            import time
            slug = f"{base_slug}-{int(time.time())}"
            break

    return slug

def add_exercises_from_file():
    """Add exercises from extracted_exercises.txt to database"""
    session = SessionLocal()
    try:
        # Read exercises from file
        exercises_file = '../extracted_exercises.txt'
        if not os.path.exists(exercises_file):
            logger.error(f"File not found: {exercises_file}")
            return

        with open(exercises_file, 'r') as f:
            lines = f.readlines()

        logger.info(f"📖 Reading {len(lines)} exercises from file...")

        added_count = 0
        skipped_count = 0

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Parse line (format: "number. exercise name")
            match = re.match(r'^\d+\.\s*(.+)$', line)
            if not match:
                continue

            exercise_name = match.group(1).strip()

            # Skip if exercise already exists (check by name)
            existing = session.query(Exercise).filter_by(name=exercise_name).first()
            if existing:
                skipped_count += 1
                if skipped_count % 100 == 0:
                    logger.info(f"📊 Progress: {added_count} added, {skipped_count} skipped (duplicates)")
                continue

            # Categorize exercise
            category, muscle_groups = categorize_exercise(exercise_name)
            equipment = determine_equipment(exercise_name)
            difficulty = determine_difficulty(exercise_name)
            slug = create_slug(exercise_name, session)

            # Create exercise
            exercise = Exercise(
                name=exercise_name,
                slug=slug,
                description=f"Exercise targeting {', '.join(muscle_groups).lower()}",
                instructions=[
                    "Set up in the starting position",
                    "Perform the movement with controlled form",
                    "Focus on engaging the target muscles",
                    "Return to starting position"
                ],
                tips=[
                    "Maintain proper form throughout",
                    "Control the movement speed",
                    "Breathe properly during execution"
                ],
                muscle_groups=muscle_groups,
                equipment=equipment,
                difficulty=difficulty,
                exercise_type="strength",
                category=category,
                duration_minutes=3,
                calories_per_minute=5.0,
                is_active=True
            )

            session.add(exercise)
            added_count += 1

            # Commit every 50 exercises to avoid large transactions
            if added_count % 50 == 0:
                try:
                    session.commit()
                    logger.info(f"📊 Progress: {added_count} exercises added, {skipped_count} skipped")
                except Exception as e:
                    session.rollback()
                    logger.error(f"❌ Error committing batch at exercise {i+1}: {e}")
                    # Continue with next batch

        # Final commit
        try:
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"❌ Error in final commit: {e}")

        # Get final count
        total_exercises = session.query(Exercise).count()

        logger.info(f"✅ Import complete!")
        logger.info(f"📊 Added: {added_count} new exercises")
        logger.info(f"📊 Skipped: {skipped_count} duplicates")
        logger.info(f"📊 Total exercises in database: {total_exercises}")

    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error adding exercises: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    add_exercises_from_file()
