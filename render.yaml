services:
  - type: web
    name: wibefit-backend
    env: python
    buildCommand: "pip install -r requirements.txt"
    startCommand: "uvicorn main:app --host 0.0.0.0 --port $PORT"
    envVars:
      - key: DATABASE_URL
        value: "postgresql://wibefit_user:wibefit_password@localhost:5432/wibefit_db"
      - key: SECRET_KEY
        value: "your-secret-key-here"
      - key: ALGORITHM
        value: "HS256"
      - key: ACCESS_TOKEN_EXPIRE_MINUTES
        value: "30"
