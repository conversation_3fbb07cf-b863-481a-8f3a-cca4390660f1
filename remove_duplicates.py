#!/usr/bin/env python3
"""
Remove duplicate exercises from the database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.exercise import Exercise
from sqlalchemy import text, func
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def remove_duplicates():
    """Remove duplicate exercises, keeping the one with the lowest ID"""
    session = SessionLocal()
    try:
        logger.info("🔍 Analyzing duplicate exercises...")
        
        # Find duplicates
        duplicates_query = session.query(
            Exercise.name, 
            func.count(Exercise.id).label('count')
        ).group_by(Exercise.name).having(func.count(Exercise.id) > 1)
        
        duplicates = duplicates_query.all()
        logger.info(f"Found {len(duplicates)} exercise names with duplicates")
        
        total_to_remove = 0
        for name, count in duplicates:
            total_to_remove += count - 1
            logger.info(f"  '{name}': {count} copies ({count-1} to remove)")
        
        logger.info(f"Total duplicate records to remove: {total_to_remove}")
        
        if total_to_remove == 0:
            logger.info("✅ No duplicates found!")
            return
        
        # Get current count
        current_count = session.query(Exercise).count()
        logger.info(f"Current total exercises: {current_count}")
        
        # Remove duplicates using a more efficient approach
        logger.info("🧹 Removing duplicates...")
        
        # Create a temporary table with unique exercises (keeping lowest ID)
        session.execute(text("""
            CREATE TEMP TABLE unique_exercises AS
            SELECT DISTINCT ON (name) *
            FROM exercises
            ORDER BY name, id;
        """))
        
        # Delete all exercises
        session.execute(text("DELETE FROM exercises;"))
        
        # Insert back the unique exercises
        session.execute(text("""
            INSERT INTO exercises 
            SELECT * FROM unique_exercises;
        """))
        
        session.commit()
        
        # Get final count
        final_count = session.query(Exercise).count()
        removed_count = current_count - final_count
        
        logger.info(f"✅ Cleanup complete!")
        logger.info(f"📊 Duplicates removed: {removed_count}")
        logger.info(f"📊 Final exercise count: {final_count}")
        
        return final_count
        
    except Exception as e:
        session.rollback()
        logger.error(f"❌ Error during cleanup: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    remove_duplicates()
