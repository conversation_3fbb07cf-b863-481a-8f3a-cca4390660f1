#!/bin/bash

# ============================================================================
# WibeFit App - Quick Setup Script
# ============================================================================
# This script helps set up the WibeFit development environment quickly
# Run with: chmod +x setup.sh && ./setup.sh

set -e  # Exit on any error

echo "🏋️ WibeFit App - Quick Setup Script"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Flutter
    if command_exists flutter; then
        FLUTTER_VERSION=$(flutter --version | head -n 1 | cut -d ' ' -f 2)
        print_success "Flutter found: $FLUTTER_VERSION"
    else
        print_error "Flutter not found. Please install Flutter first."
        print_status "Download from: https://docs.flutter.dev/get-started/install"
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version | cut -d ' ' -f 2)
        print_success "Python found: $PYTHON_VERSION"
    elif command_exists python; then
        PYTHON_VERSION=$(python --version | cut -d ' ' -f 2)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python not found. Please install Python 3.8+ first."
        print_status "Download from: https://python.org/downloads/"
        exit 1
    fi
    
    # Check Git
    if command_exists git; then
        print_success "Git found"
    else
        print_warning "Git not found. Some features may not work."
    fi
    
    echo ""
}

# Setup Flutter dependencies
setup_flutter() {
    print_status "Setting up Flutter dependencies..."
    
    cd frontend
    
    # Run flutter doctor
    print_status "Running Flutter doctor..."
    flutter doctor
    
    # Get dependencies
    print_status "Installing Flutter dependencies..."
    flutter pub get
    
    print_success "Flutter setup complete!"
    cd ..
    echo ""
}

# Setup Python backend
setup_backend() {
    print_status "Setting up Python backend..."
    
    cd backend
    
    # Determine Python command
    if command_exists python3; then
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
    else
        PYTHON_CMD="python"
        PIP_CMD="pip"
    fi
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        $PYTHON_CMD -m venv venv
    else
        print_status "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    print_success "Backend setup complete!"
    cd ..
    echo ""
}

# Check available devices
check_devices() {
    print_status "Checking available devices..."
    
    cd frontend
    flutter devices
    cd ..
    echo ""
}

# Main setup function
main() {
    echo "Starting WibeFit setup process..."
    echo ""
    
    # Check if we're in the right directory
    if [ ! -f "requirements.txt" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        print_error "Please run this script from the WibeFit project root directory"
        exit 1
    fi
    
    # Run setup steps
    check_requirements
    setup_flutter
    setup_backend
    check_devices
    
    # Final instructions
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    echo "Next steps:"
    echo "1. Start the backend server:"
    echo "   cd backend"
    echo "   source venv/bin/activate  # Activate virtual environment"
    echo "   python wibefit_postgresql_server.py  # PostgreSQL server"
    echo "   # OR"
    echo "   python wibefit_v1_server.py  # SQLite server"
    echo ""
    echo "2. In a new terminal, start the Flutter app:"
    echo "   cd frontend"
    echo "   flutter run"
    echo ""
    echo "3. Choose your target device when prompted"
    echo ""
    echo "📖 For detailed instructions, see README.md"
    echo "🆘 For issues, check requirements.txt troubleshooting section"
    echo ""
    print_success "Happy coding! 🚀"
}

# Run main function
main "$@"
