# WibeFit Server Cloud Deployment Guide

## Option 1: Railway (Recommended - Free Tier Available)

1. **Sign up at Railway.app**
   - Go to https://railway.app
   - Sign up with GitHub

2. **Deploy from GitHub**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Add cloud deployment"
   git push origin main
   ```

3. **Create Railway Project**
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your repository
   - Railway will auto-detect the Dockerfile

4. **Set Environment Variables**
   - Go to Variables tab
   - Add: `PORT=8000`
   - Add: `DATABASE_PATH=/app/data/wibefit.db`

5. **Get Your URL**
   - Railway will provide a URL like: `https://your-app.railway.app`
   - Update your Flutter app to use this URL

## Option 2: Heroku (Free Tier Discontinued)

1. **Install Heroku CLI**
   ```bash
   # macOS
   brew tap heroku/brew && brew install heroku
   
   # Login
   heroku login
   ```

2. **Create Heroku App**
   ```bash
   heroku create your-wibefit-app
   git push heroku main
   ```

## Option 3: DigitalOcean App Platform

1. **Sign up at DigitalOcean**
2. **Create App from GitHub**
3. **Configure Build Settings**
   - Build Command: `docker build .`
   - Run Command: `python wibefit_sqlite_server.py`

## Option 4: Local Docker (For Development)

```bash
# Build and run locally
./deploy.sh

# Your server will be available at:
# http://localhost:8000
```

## Update Flutter App

After deployment, update your Flutter app:

```dart
// In hybrid_database_service.dart
static const String _onlineBaseUrl = 'https://your-app.railway.app';
```

## Testing Cross-Device Login

1. **Deploy server to cloud**
2. **Update Flutter app with cloud URL**
3. **Build new APK**
4. **Install on multiple devices**
5. **Register on Device A**
6. **Login on Device B** ✅

## Benefits of Cloud Deployment

✅ **Cross-Device Login**: Register once, login anywhere
✅ **Data Sync**: Shared data across all devices
✅ **Always Available**: 24/7 server uptime
✅ **Automatic Backups**: Cloud provider handles backups
✅ **Scalability**: Handles multiple users
