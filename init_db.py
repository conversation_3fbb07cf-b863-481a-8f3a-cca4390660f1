#!/usr/bin/env python3
import sqlite3
import hashlib
import json

def hash_password(password):
    salt = 'wibefit_salt_2024'
    return hashlib.sha256((password + salt).encode()).hexdigest()

def init_database():
    """Initialize SQLite database with required tables and sample data"""
    try:
        conn = sqlite3.connect('wibefit.db')
        cursor = conn.cursor()

        # Create user_profiles table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_profiles (
                username TEXT PRIMARY KEY,
                age INTEGER,
                phone TEXT,
                gender TEXT,
                height REAL,
                current_weight REAL,
                target_weight REAL,
                fitness_level TEXT DEFAULT 'beginner',
                activity_level TEXT DEFAULT 'moderate',
                fitness_goals TEXT DEFAULT '[]',
                equipment_access TEXT DEFAULT '[]',
                workout_frequency INTEGER DEFAULT 3,
                preferred_workout_duration INTEGER DEFAULT 45,
                health_conditions TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Insert sample profile for 'sanju' user
        cursor.execute('''
            INSERT OR REPLACE INTO user_profiles 
            (username, age, gender, height, current_weight, target_weight, fitness_level, activity_level, fitness_goals, equipment_access, workout_frequency) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('sanju', 25, 'male', 175.0, 70.0, 65.0, 'intermediate', 'active', '["weight_loss", "muscle_gain"]', '["dumbbells", "bodyweight"]', 4))

        # Insert sample profile for 'demo' user
        cursor.execute('''
            INSERT OR REPLACE INTO user_profiles 
            (username, age, gender, height, current_weight, target_weight, fitness_level, activity_level, fitness_goals, equipment_access, workout_frequency) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('demo', 28, 'male', 175.0, 75.0, 70.0, 'intermediate', 'moderate', '["weight_loss", "muscle_gain"]', '["dumbbells", "resistance_bands"]', 4))

        conn.commit()
        conn.close()
        print('✅ Database created and sample data inserted successfully')
        return True

    except Exception as e:
        print(f'❌ Database initialization error: {e}')
        return False

if __name__ == '__main__':
    init_database()
