#!/usr/bin/env python3
"""
Embed SQLite database directly in Flutter app for offline use
"""

import shutil
import os

def embed_database_in_flutter_app():
    """Copy SQLite database to Flutter app assets"""
    try:
        # Source database file
        source_db = "wibefit.db"
        
        # Target directory in Flutter app
        target_dir = "../frontend/assets/database"
        target_db = os.path.join(target_dir, "wibefit.db")
        
        # Create target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)
        
        # Copy database file
        if os.path.exists(source_db):
            shutil.copy2(source_db, target_db)
            print(f"✅ Database copied to: {target_db}")
            print(f"📊 Database size: {os.path.getsize(target_db) / 1024 / 1024:.2f} MB")
            
            # Update pubspec.yaml to include the database
            pubspec_path = "../frontend/pubspec.yaml"
            if os.path.exists(pubspec_path):
                with open(pubspec_path, 'r') as f:
                    content = f.read()
                
                # Check if assets section exists
                if "assets:" not in content:
                    # Add assets section
                    flutter_section = content.find("flutter:")
                    if flutter_section != -1:
                        # Find the end of flutter section
                        lines = content.split('\n')
                        flutter_line = -1
                        for i, line in enumerate(lines):
                            if line.strip().startswith("flutter:"):
                                flutter_line = i
                                break
                        
                        if flutter_line != -1:
                            # Insert assets section after flutter:
                            lines.insert(flutter_line + 1, "  assets:")
                            lines.insert(flutter_line + 2, "    - assets/database/")
                            
                            with open(pubspec_path, 'w') as f:
                                f.write('\n'.join(lines))
                            
                            print("✅ Updated pubspec.yaml with database assets")
                        else:
                            print("⚠️ Could not find flutter: section in pubspec.yaml")
                    else:
                        print("⚠️ Could not find flutter: section in pubspec.yaml")
                elif "assets/database/" not in content:
                    # Add database to existing assets
                    lines = content.split('\n')
                    assets_line = -1
                    for i, line in enumerate(lines):
                        if line.strip().startswith("assets:"):
                            assets_line = i
                            break
                    
                    if assets_line != -1:
                        lines.insert(assets_line + 1, "    - assets/database/")
                        with open(pubspec_path, 'w') as f:
                            f.write('\n'.join(lines))
                        print("✅ Added database to existing assets in pubspec.yaml")
                    else:
                        print("⚠️ Could not find assets: section in pubspec.yaml")
                else:
                    print("✅ Database already configured in pubspec.yaml")
            else:
                print("⚠️ pubspec.yaml not found")
            
            return True
        else:
            print(f"❌ Source database not found: {source_db}")
            return False
            
    except Exception as e:
        print(f"❌ Error embedding database: {e}")
        return False

def create_offline_database_service():
    """Create a Flutter service to use the embedded database"""
    service_content = '''import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class OfflineDatabaseService {
  static Database? _database;
  static const String _databaseName = 'wibefit.db';
  static const String _databaseVersion = '1.0';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    try {
      // Get the path to the documents directory
      final documentsDirectory = await getDatabasesPath();
      final path = join(documentsDirectory, _databaseName);

      // Check if database already exists
      final exists = await databaseExists(path);
      
      if (!exists) {
        print('📦 Copying database from assets...');
        
        // Copy from assets
        final ByteData data = await rootBundle.load('assets/database/$_databaseName');
        final List<int> bytes = data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
        
        // Write to documents directory
        await File(path).writeAsBytes(bytes);
        print('✅ Database copied successfully');
      } else {
        print('✅ Database already exists');
      }

      // Open the database
      final database = await openDatabase(
        path,
        version: 1,
        readOnly: false,
      );

      print('✅ Offline database initialized: $path');
      return database;
      
    } catch (e) {
      print('❌ Error initializing offline database: $e');
      rethrow;
    }
  }

  // Exercise queries
  static Future<List<Map<String, dynamic>>> getExercises({
    int limit = 20,
    int offset = 0,
    String? category,
    String? search,
  }) async {
    final db = await database;
    
    String whereClause = 'is_active = 1';
    List<dynamic> whereArgs = [];

    if (search != null && search.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      whereArgs.addAll(['%$search%', '%$search%']);
    }

    if (category != null && category.isNotEmpty) {
      whereClause += ' AND category = ?';
      whereArgs.add(category.toLowerCase());
    }

    final result = await db.query(
      'exercises',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'name',
      limit: limit,
      offset: offset,
    );

    return result;
  }

  static Future<int> getExercisesCount({
    String? category,
    String? search,
  }) async {
    final db = await database;
    
    String whereClause = 'is_active = 1';
    List<dynamic> whereArgs = [];

    if (search != null && search.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      whereArgs.addAll(['%$search%', '%$search%']);
    }

    if (category != null && category.isNotEmpty) {
      whereClause += ' AND category = ?';
      whereArgs.add(category.toLowerCase());
    }

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM exercises WHERE $whereClause',
      whereArgs,
    );

    return result.first['count'] as int;
  }

  // User profile queries
  static Future<Map<String, dynamic>?> getUserProfile(String username) async {
    final db = await database;
    
    final result = await db.query(
      'user_profiles',
      where: 'username = ?',
      whereArgs: [username],
      limit: 1,
    );

    return result.isNotEmpty ? result.first : null;
  }

  // User queries
  static Future<Map<String, dynamic>?> getUser(String username) async {
    final db = await database;
    
    final result = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
      limit: 1,
    );

    return result.isNotEmpty ? result.first : null;
  }

  // Close database
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}'''

    service_path = "../frontend/lib/core/services/offline_database_service.dart"
    try:
        with open(service_path, 'w') as f:
            f.write(service_content)
        print(f"✅ Created offline database service: {service_path}")
        return True
    except Exception as e:
        print(f"❌ Error creating offline database service: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Embedding SQLite database in Flutter app...")
    
    if embed_database_in_flutter_app():
        print("✅ Database embedded successfully!")
        
        if create_offline_database_service():
            print("✅ Offline database service created!")
            print("")
            print("📱 Next steps:")
            print("1. Run 'flutter pub get' to update dependencies")
            print("2. Add 'sqflite' dependency to pubspec.yaml if not already present")
            print("3. Use OfflineDatabaseService instead of HTTP API calls")
            print("4. The app will now work completely offline!")
        else:
            print("❌ Failed to create offline database service")
    else:
        print("❌ Failed to embed database")
