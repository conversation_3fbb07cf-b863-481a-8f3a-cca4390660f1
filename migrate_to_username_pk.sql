-- Migration script to change primary key from id to username
-- This will make username the primary key and ensure uniqueness

BEGIN;

-- Step 1: Add username column to users table
ALTER TABLE users ADD COLUMN username TEXT;

-- Step 2: Update existing users with usernames based on email
UPDATE users SET username = SPLIT_PART(email, '@', 1) WHERE username IS NULL;

-- Step 3: Ensure all usernames are unique by adding numbers if needed
WITH numbered_users AS (
  SELECT id, username, 
         ROW_NUMBER() OVER (PARTITION BY username ORDER BY created_at) as rn
  FROM users
)
UPDATE users 
SET username = CASE 
  WHEN numbered_users.rn = 1 THEN numbered_users.username
  ELSE numbered_users.username || numbered_users.rn::text
END
FROM numbered_users 
WHERE users.id = numbered_users.id;

-- Step 4: Make username NOT NULL
ALTER TABLE users ALTER COLUMN username SET NOT NULL;

-- Step 5: Create new tables with username as primary key
CREATE TABLE users_new (
    username TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb
);

CREATE TABLE user_profiles_new (
    username TEXT PRIMARY KEY REFERENCES users_new(username) ON DELETE CASCADE,
    age INTEGER,
    phone TEXT,
    gender TEXT,
    height REAL,
    current_weight REAL,
    fitness_level TEXT,
    primary_goals JSONB,
    equipment_access JSONB,
    preferred_workout_days JSONB,
    activity_level TEXT,
    preferred_workout_duration INTEGER,
    profile_setup_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE workout_sessions_new (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL REFERENCES users_new(username) ON DELETE CASCADE,
    workout_name TEXT NOT NULL,
    exercises JSONB NOT NULL DEFAULT '[]'::jsonb,
    duration_minutes INTEGER,
    calories_burned REAL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE body_weights_new (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL REFERENCES users_new(username) ON DELETE CASCADE,
    weight REAL NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

-- Step 6: Migrate data to new tables
INSERT INTO users_new (username, email, full_name, role, created_at, last_login_at, metadata)
SELECT username, email, full_name, role, created_at, last_login_at, metadata
FROM users;

INSERT INTO user_profiles_new (username, age, phone, gender, height, current_weight,
                              fitness_level, primary_goals, equipment_access, preferred_workout_days,
                              activity_level, preferred_workout_duration, profile_setup_completed,
                              created_at, updated_at)
SELECT u.username, up.age, up.phone, up.gender, up.height, up.current_weight,
       up.fitness_level, up.primary_goals, up.equipment_access, up.preferred_workout_days,
       up.activity_level, up.preferred_workout_duration, up.profile_setup_completed,
       up.profile_setup_date, up.updated_at
FROM user_profiles up
JOIN users u ON up.user_id = u.id;

INSERT INTO workout_sessions_new (username, workout_name, exercises, duration_minutes, calories_burned,
                                 completed_at, notes, created_at)
SELECT u.username, ws.workout_name, ws.exercises, ws.duration_minutes, ws.calories_burned,
       ws.completed_at, ws.notes, ws.created_at
FROM workout_sessions ws
JOIN users u ON ws.user_id = u.id;

INSERT INTO body_weights_new (username, weight, recorded_at, notes)
SELECT u.username, bw.weight, bw.recorded_at, bw.notes
FROM body_weights bw
JOIN users u ON bw.user_id = u.id;

-- Step 7: Drop old tables and rename new ones
DROP TABLE body_weights CASCADE;
DROP TABLE workout_sessions CASCADE;
DROP TABLE user_profiles CASCADE;
DROP TABLE users CASCADE;

ALTER TABLE users_new RENAME TO users;
ALTER TABLE user_profiles_new RENAME TO user_profiles;
ALTER TABLE workout_sessions_new RENAME TO workout_sessions;
ALTER TABLE body_weights_new RENAME TO body_weights;

-- Step 8: Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_profiles_username ON user_profiles(username);
CREATE INDEX idx_workout_sessions_username ON workout_sessions(username);
CREATE INDEX idx_body_weights_username ON body_weights(username);

COMMIT;
